import { MEDIA_TYPE } from '#constants/media'
import { TRACKING_ACTION } from '#constants/tracking'
import ZnPost, { EPostSource, EPostType } from '#models/zn_post'
import ZnPostCategory from '#models/zn_post_category'
import ZnPostTranslation from '#models/zn_post_translation'
import JwtService from '#services/jwt_service'
import { PostService } from '#services/post_service'
import { HttpContext } from '@adonisjs/core/http'
import { DateTime } from 'luxon'
import moment from 'moment'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'
import { uploadMediasFromLinks } from '../../../services/media/index.js'
import { createZurnoPostValidator } from '../../validators/post/zurno_post_validator.js'
import { StoreService } from "#services/store_service";

export default class AdminZurnoPostController {
  private postService = new PostService()
  private storeService = new StoreService()

  /**
   * @index
   * @tag Admin Zurno Post
   * @summary Read all posts
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnPost[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null,"price":"200","isDraft":"0","isFavourite":"1","expired":"0","categories":["ZnCategory"]).with(thumbnail, medias, user).paginated() - Read all posts descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.ZURNO_POST)

    try {
      const {
        page = 1,
        limit = 10,
        search,
        latitude,
        longitude,
        // isDraft,
        // isFavourite,
        // priceFrom,
        // priceTo,
        filter,
        lazyFilter,
        sort,
        sortBy,
        miles,
      } = request.qs()

      const query = ZnPost.query()
        .preload('categories', (categoryQuery) => {
          categoryQuery.preload('thumbnail')
        })
        .preload('medias')
        .preload('thumbnail')
        .preload('createdByAdmin')
        .whereNull('deletedAt')
        .where('source', EPostSource.ZURNO)

      if (search) {
        const translations = await ZnPostTranslation.query().whereRaw(
          'LOWER(value) LIKE LOWER(?)',
          [`%${search}%`]
        )

        query.where((queryBuilder) => {
          queryBuilder
            .whereRaw('LOWER(title) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(description) LIKE LOWER(?)', [`%${search}%`])
            .orWhereIn(
              'id',
              translations.map((translation) => translation.postId)
            )
        })
      }

      // if (isFavourite) {
      //   query.where('isFavourite', isFavourite)
      // }

      if (filter) {
        const statuses: string[] = []
        filter.map((fil: string) => {
          if (fil.startsWith('fromDate')) {
            const fromDate = moment(fil.split('=')[1]).format('yyyy-MM-DD')
            query.whereRaw(`createdAt >= '${fromDate}'`)
          } else if (fil.startsWith('toDate')) {
            const toDate = moment(fil.split('=')[1]).add(1, 'day').format('yyyy-MM-DD')
            query.whereRaw(`createdAt <= '${toDate}'`)
          } else if (fil.startsWith('status')) {
            statuses.push(fil.split('=')[1])
          }
        })

        query.where((queryBuilder) => {
          for (const status of statuses) {
            switch (status) {
              case 'scheduled': {
                queryBuilder.orWhere((queryBuild) => {
                  queryBuild
                    .where('isDraft', true)
                    .whereNotNull('scheduledAt')
                })
                break
              }
              case 'draft': {
                queryBuilder.orWhere((queryBuild) => {
                  queryBuild
                    .where('isDraft', true)
                    .whereNull('scheduledAt')
                })
                break
              }
              case 'unlist': {
                queryBuilder.orWhere((queryBuild) => {
                  queryBuild
                    .where('isDraft', false)
                    .where('isUnlist', true)
                })
                break
              }
              case 'published': {
                queryBuilder.orWhere((queryBuild) => {
                  queryBuild
                    .where('isDraft', false)
                    .where('isUnlist', false)
                })
                break
              }
            }
          }
        })
      }

      if (lazyFilter) {
        const categoryIds: string[] = []
        const adminIds: string[] = []

        lazyFilter.map((fil: string) => {
          if (fil.startsWith('categoryId')) {
            categoryIds.push(fil.split('=')[1])
            // query.whereHas('categories', (categoryQuery) => {
            //   categoryQuery.where('zn_post_categories.id', fil.split("=")[1])
            // })
          } else if (fil.startsWith('createdByAdminId')) {
            adminIds.push(fil.split('=')[1])
            // query.whereHas('createdByAdmin', (adminQuery) => {
            //   adminQuery.where('zn_admins.id', fil.split("=")[1])
            // })
          }
        })

        if (categoryIds.length > 0) {
          query.whereHas('categories', (categoryQuery) => {
            categoryQuery.whereIn('zn_post_categories.id', categoryIds)
          })
        }
        if (adminIds.length > 0) {
          query.whereIn('createdByAdminId', adminIds)
        }
      }

      if (sort || sortBy) {
        if (sortBy === 'distance' && latitude && longitude) {
          const lat = Number.parseFloat(latitude)
          const lon = Number.parseFloat(longitude)

          query.where((queryBuilder) => {
            queryBuilder
              .orWhereRaw(
                `
              (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) <= 100
              `,
                [lat, lon, lat]
              )
              .orWhereHas('store', (storeQuery) => {
                storeQuery.whereRaw(
                  `
                (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) <= 100
                `,
                  [lat, lon, lat]
                )
              })
          })
        }

        if (sortBy === 'distance' && latitude && longitude && miles) {
          const lat = Number.parseFloat(latitude)
          const lon = Number.parseFloat(longitude)
          const maxDistance = Number.parseFloat(miles)

          query.where((queryBuilder) => {
            queryBuilder
              .whereRaw(
                `
                (3959 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) <= ?
                `,
                [lat, lon, lat, maxDistance]
              )
              .orWhereHas('store', (storeQuery) => {
                storeQuery.whereRaw(
                  `
                  (3959 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) <= ?
                  `,
                  [lat, lon, lat, maxDistance]
                )
              })
          })
        }

        if (sortBy === 'popular') {
          query.orderBy('isFavourite', 'desc')
        }

        if (sortBy === 'newest') {
          query.orderBy('createdAt', 'desc')
        }

        if (sort && sort.length > 0) {
          query.orderBy(sort[0], sort[1])
        }
      } else {
        query.orderBy('createdAt', 'desc')
      }

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @create
   * @tag Admin Zurno Post
   * @summary Return info for creation
   * @responseBody 200 - {"info":{"postCaegories":"<ZnPostCategory>"}} - Return info for creation
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display form to create a new record
   */
  async create({ bouncer, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.ZURNO_POST)

    try {
      const postCategories = await ZnPostCategory.query()
        .select('id', 'name')
        .whereNull('deletedAt')
        .whereNull('parentId')

      return response.ok({
        info: {
          postCategories: postCategories,
        },
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @store
   * @tag Admin Zurno Post
   * @summary Create action
   * @requestBody <ZnPost>.only(title, description, thumbnailId, youtubeUrl, createdByAdminId)
   * @responseBody 201 - <ZnPost>.append("id":""") - Create action descriptively
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The title field must be defined","rule":"required","field":"title"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Handle form submission for the create action
   */
  async store({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.ZURNO_POST)

    // @ts-ignore
    const data = request.all()

    const payload = await createZurnoPostValidator.validate(data)
    const zurnoStore = await this.storeService.getZurnoStore()
    try {
      let thumbnailId
      if (payload.thumbnailUrl) {
        const thumbnails = await uploadMediasFromLinks([payload.thumbnailUrl], MEDIA_TYPE.IMAGE)
        if (thumbnails.length > 0) {
          thumbnailId = thumbnails[0]?.id
        }
      } else if (payload.thumbnailId) {
        thumbnailId = payload.thumbnailId
      }

      const created = await ZnPost.create({
        source: EPostSource.ZURNO,
        storeId: zurnoStore?.id || null,

        title: payload.title,
        description: payload.description ?? undefined,

        isFavourite: payload.isFavourite !== undefined ? payload.isFavourite : (false as any),
        isUnlist: payload.isUnlist !== undefined ? payload.isUnlist : (false as any),
        isDraft: payload.isDraft !== undefined ? payload.isDraft : (false as any),

        thumbnailId: thumbnailId ?? undefined,

        youtubeUrl: payload.youtubeUrl ?? undefined,
        createdByAdminId: payload.createdByAdminId,

        type: payload.type,
        scheduledAt: payload.scheduledAt ? DateTime.fromJSDate(payload.scheduledAt) : null
      })

      // If scheduledAt is set, default to draft
      if (created.scheduledAt) { created.isDraft = true }

      if (payload.mediaIds && payload.mediaIds.length > 0) {
        await created.related('medias').sync(payload.mediaIds)
      }

      if (payload.timelines) {
        await this.postService.updateOrCreateVideoTimelines(created.id, payload.timelines)
      }

      if (payload.categoryIds && payload.categoryIds.length > 0) {
        await created.related('categories').sync(payload.categoryIds)
      }

      switch (payload.resourceType) {
        case 'product': {
          created.productId = payload.productId || created.productId
          created.collectionId = null
          break
        }
        case 'collection': {
          created.collectionId = payload.collectionId || created.collectionId
          created.productId = null
          break
        }
        default: {
          created.productId = null
          created.collectionId = null
        }
      }

      if (payload.type == EPostType.VIDEO) {
        await this.postService.generateThumbnailAsync({ postId: created.id })
      }

      const newCreated = await created.save()

      return response.created(newCreated)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
        error: error,
      })
    }
  }

  /**
   * @show
   * @tag Admin Zurno Post
   * @summary Read a post
   * @paramPath id - ID of Post - @type(string) @required
   * @responseBody 200 - <ZnPost>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null,"mine":false) - Read a post descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Post not found"} - Not Found
   */
  /**
   * Show individual record
   */
  async show({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.ZURNO_POST)

    try {
      let requestUserId

      const authToken = request.header('Authorization') as string

      JwtService.decodeToken(authToken, (decodedToken: Partial<{ userId: string }>) => {
        if (decodedToken) { requestUserId = decodedToken.userId }
      })

      const postId = params.id

      const post = await ZnPost.query()
        .preload('categories', (categoryQuery) => {
          categoryQuery.preload('thumbnail')
        })
        .preload('stream')
        .preload('medias')
        .preload('thumbnail')
        .preload('product')
        .preload('collection')
        .where('id', postId)
        .where('source', EPostSource.ZURNO)
        .first()

      return response.ok({
        ...post?.serialize(),
        mine: post?.userId === requestUserId,
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @edit
   * @tag Admin Zurno Post
   * @summary Return info for updating
   * @responseBody 200 - {"data":"<ZnPost>"}} - Return info for creation
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Edit individual record
   */
  async edit({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.ZURNO_POST)

    try {
      let requestUserId
      const postId = params.id

      const authToken = request.header('Authorization') as string

      JwtService.decodeToken(authToken, (decodedToken: Partial<{ userId: string }>) => {
        if (decodedToken) { requestUserId = decodedToken.userId }
      })

      const post = await ZnPost.query()
        .preload('categories', (categoryQuery) => {
          categoryQuery.preload('thumbnail')
        })
        .preload('stream')
        .preload('medias')
        .preload('thumbnail')
        .preload('product')
        .preload('collection')
        .preload('translations')
        .preload('timelines', (timelineQuery) => {
          timelineQuery.preload('variant', (variantQuery) => {
            variantQuery.preload('image')
              .preload('product', (productQuery) => {
                productQuery.preload('image')
              })
          })
        })
        .where('id', postId)
        .where('source', EPostSource.ZURNO)
        .first()

      if (!post) {
        return response.notFound({ message: 'Post not found' })
      }

      const postService = new PostService()

      // return {[TRACKING_ACTION:number]:{count:"number",users:[]}}
      const interactions = await postService.getInteractions(post.id, [
        TRACKING_ACTION.VIEW_POST,
        TRACKING_ACTION.ADD_WISHLIST,
        TRACKING_ACTION.SHARE,
      ])

      // added to cart variants
      const addedToCart = await this.postService.getAddedToCartVariants(post.id)

      return response.ok({
        data: {
          ...post?.serialize(),
          mine: post?.userId === requestUserId,
          interactions,
          addedToCart,
        },
      })
    } catch (error) {
      console.log(error);

      return response.badRequest(error)
    }
  }

  /**
   * @update
   * @tag Admin Zurno Post
   * @summary Update a post
   * @description Update a post descriptively
   * @paramPath id - ID of Post - @type(string) @required
   * @requestBody <ZnPost>.only(title, description, thumbnailId, youtubeUrl, createdByAdminId)
   * @responseBody 200 - <ZnPost>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The title field must be defined","rule":"required","field":"title"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Post not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.ZURNO_POST)

    const postId = params.id

    const post = await ZnPost.query()
      .where('id', postId)
      .where('source', EPostSource.ZURNO)
      .preload('timelines')
      .first()

    if (!post) {
      return response.notFound({ message: 'Post not found' })
    }

    const data = request.all()

    const payload = await createZurnoPostValidator.validate(data)

    try {
      post.title = payload.title
      post.description = payload.description || post.description

      post.isFavourite =
        payload.isFavourite !== undefined && payload.isFavourite !== null
          ? payload.isFavourite
          : (post.isFavourite as any)
      post.isUnlist =
        payload.isUnlist !== undefined && payload.isUnlist !== null
          ? payload.isUnlist
          : (post.isUnlist as any)
      post.isDraft =
        payload.isDraft !== undefined && payload.isDraft !== null
          ? payload.isDraft
          : (post.isDraft as any)

      // If scheduledAt is set, default to draft
      if (post.scheduledAt) { post.isDraft = true }

      let thumbnailId
      if (payload.thumbnailUrl) {
        const thumbnails = await uploadMediasFromLinks([payload.thumbnailUrl], MEDIA_TYPE.IMAGE)
        if (thumbnails.length > 0) {
          thumbnailId = thumbnails[0]?.id
        }
      } else if (payload.thumbnailId) {
        thumbnailId = payload.thumbnailId
      }

      post.thumbnailId = thumbnailId || null

      if (Array.isArray(payload.mediaIds)) {
        await post.related('medias').sync(payload.mediaIds)
      }

      if (payload.timelines) {
        await this.postService.updateOrCreateVideoTimelines(post.id, payload.timelines)
      }

      if (Array.isArray(payload.categoryIds)) {
        await post.related('categories').sync(payload.categoryIds)
      }

      post.youtubeUrl = payload.youtubeUrl || post.youtubeUrl

      post.originLocale = payload.originLocale || null
      if (payload.translations) {
        for (const translation of payload.translations) {
          const translate = await ZnPostTranslation.find(translation.id || '')
          if (translate) {
            translate.value = translation.value
            await translate.save()
          }
          // else {
          //   await ZnPostTranslation.create({
          //     postId: post.id,
          //     locale: translation.locale,
          //     field: translation.field,
          //     value: translation.value,
          //   })
          // }
        }
      }

      switch (payload.resourceType) {
        case 'product': {
          post.productId = payload.productId || post.productId
          post.collectionId = null
          break
        }
        case 'collection': {
          post.collectionId = payload.collectionId || post.collectionId
          post.productId = null
          break
        }
        default: {
          post.productId = null
          post.collectionId = null
        }
      }

      const updated = await post.save()

      return response.ok(updated)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
        error: error,
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Zurno Post
   * @summary Soft-delete a post
   * @description Soft-delete a post descriptively
   * @paramPath id - ID of Post - @type(string) @required
   * @responseBody 200 - {"message":"Post soft-deleted successfully"} - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Post not found"} - Not Found
   */
  /**
   * Delete record
   */
  async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.ZURNO_POST)

    const postId = params.id

    const post = await ZnPost.query().where('id', postId).where('source', EPostSource.ZURNO).first()

    if (!post) {
      return response.notFound({ message: 'Post not found' })
    }

    await post.softDelete()

    return response.ok({ message: 'Post soft-deleted successfully' })
  }

  /**
   * @deleteLocaleTranslation
   * @tag Admin Zurno Post
   * @summary Delete a locale translation
   * @description Delete a locale translation descriptively
   * @paramPath id - ID of Post - @type(string) @required
   * @requestBody {"postId": "", "local": "en"}
   * @responseBody 200 - {"message":"Post translations deleted successfully"} - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Post not found"} - Not Found
   */
  async deleteLocaleTranslation({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.ACCESS_TRANSLATION, RESOURCE.ZURNO_POST)

    const postId = request.all().postId

    const post = await ZnPost.query().where('id', postId).where('source', EPostSource.ZURNO).first()

    if (!post) {
      return response.notFound({ message: 'Post not found' })
    }

    const translations = await ZnPostTranslation.query()
      .where('postId', postId)
      .where('locale', request.all().locale)

    translations.map(async (translation) => await translation.delete())

    return response.ok({ message: 'Post translations deleted successfully' })
  }

  /**
   * @generateThumbnail
   * @tag Admin Post
   * @summary Generate thumbnail
   * @description Generate thumbnail for video post
   * @paramPath id - ID of Post - @type(string) @required
   * @responseBody 200 - <ZnMedia> - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Post not found"} - Not Found
   */
  async generateThumbnail({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.POST)

    try {
      const postId = request.all().postId

      const post = await ZnPost.find(postId)

      if (!post) {
        return response.notFound({ message: 'Post not found' })
      }

      const thumbnail = await this.postService.generateThumbnailSync({
        postId,
        overwrite: true,
        seek: 'random'
      })

      return response.ok(thumbnail)

    } catch (error) {
      console.log(error);
      return response.internalServerError({
        message: "Something went wrong!",
        error
      })
    }
  }
}

import { ACTION, RESOURCE } from "#constants/authorization";
import VendorCommissionService from "#services/vendors/vendor_commission_service";
import { HttpContext } from "@adonisjs/core/http";

export default class AdminVendorCommissionController {
  private vendorCommissionService: VendorCommissionService;

  constructor() {
    this.vendorCommissionService = new VendorCommissionService();
  }

  async index({ bouncer, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.VENDOR);

      const { vendorId } = request.params();
      const { page = 1, limit = 10 } = request.all();

      if (vendorId) {
        const vendors = await this.vendorCommissionService.getAllCommisisonsByVendor(vendorId, page, limit);
        return response.ok(vendors);
      } else {
        const vendors = await this.vendorCommissionService.getAllCommisisons(page, limit);
        return response.ok(vendors);
      }
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async show({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.VENDOR);
      const { id } = params;
      if (!id) {
        return response.badRequest({ message: "Commission ID is required" });
      }

      const vendor = await this.vendorCommissionService.getCommissionById(id);
      return response.ok(vendor);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async update({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.VENDOR);
      const id = params.id;
      if (!id) {
        return response.badRequest({ message: "Commission ID is required" });
      }
      const data = request.all();
      const updatedVendor = await this.vendorCommissionService.update(id, data);
      return response.ok(updatedVendor);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async destroy({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.VENDOR);
      const { id } = params;
      if (!id) {
        return response.badRequest({ message: "Vendor Commission ID is required" });
      }
      await this.vendorCommissionService.delete(id);
      return response.ok({ message: "Vendor Commission deleted successfully" });
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }
}
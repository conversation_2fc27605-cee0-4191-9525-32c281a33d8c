/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'

const ProfilesController = () => import('#controllers/app/profiles_controller')

export default function profileRoutes() {
  router
    .group(() => {
      router.get('/', [ProfilesController, 'getProfile'])
      router.post('/', [ProfilesController, 'updateProfile'])
      router.post('/update-locale', [ProfilesController, 'updateLocale'])
      router.delete('/', [<PERSON>s<PERSON><PERSON>roller, 'destroyProfile'])
      router.get('/report/post-report', [ProfilesController, 'getUserPostReport'])

      router
        .group(() => {
          router.get('', [ProfilesController, 'getAddresses'])
          router.get('/zurno', [ProfilesController, 'getZurnoAddresses'])
          router.post('', [ProfilesController, 'createAddresses'])
          router.post('/default', [ProfilesController, 'setDefaultAddresses'])
          router.put('/:id', [ProfilesController, 'updateAddresses'])
          router.delete('/:id', [ProfilesController, 'deleteAddresses'])
        })
        .prefix('addresses')
    })
    .prefix('profile')
    .middleware([middleware.auth()])
}

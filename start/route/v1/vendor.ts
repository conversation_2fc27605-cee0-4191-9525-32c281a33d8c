import { middleware } from "#start/kernel";
import router from "@adonisjs/core/services/router";

const VendorController = () => import("#controllers/vendor_controller");
const VendorCommissionController = () => import("#controllers/vendor_commission_controller");

export default function vendorRoutes() {
  router
    .group(() => {
      router.post('/', [VendorController, 'store']);
    })
    .prefix('vendors');


  router
    .group(() => {
      router.get('/commissions', [VendorCommissionController, 'index']);
      router.get('/commissions/:id', [VendorCommissionController, 'show']);

      router.get('/', [VendorController, 'show']);
      router.put('/', [VendorController, 'update']);
    })
    .use(middleware.auth({ guards: ['jwt_admin', 'jwt_user'] as any }))
    .prefix('vendors');
}
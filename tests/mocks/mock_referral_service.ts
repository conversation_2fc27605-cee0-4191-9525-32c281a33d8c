import ReferralService from '#services/referral_service'
import ZnUser from '#models/zn_user'

export class MockReferralService extends ReferralService {
  async checkReferral(user: ZnUser) {
    console.log(`Mock check referral for user: ${user.email}`)
    return
  }

  async sendMail(referralId: string, name?: string) {
    console.log(`Mock send referral mail for ID: ${referralId}`, name)
    return
  }

  async sendSMS(referralId: string, name?: string) {
    console.log(`Mock send referral SMS for ID: ${referralId}`, name)
    return
  }
}

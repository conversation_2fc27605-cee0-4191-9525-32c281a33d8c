# Zurno API

## Overview

This project is built using [AdonisJS](https://adonisjs.com/), a Node.js web framework that provides a robust and elegant structure for developing server-side applications. AdonisJS follows the MVC (Model-View-Controller) pattern and is inspired by frameworks like Lara<PERSON>, making it familiar for developers with experience in similar ecosystems.

## Requirements

- Node.js (>= 20.x)
- npm (>= 10.x)
- AdonisJS CLI (optional, but recommended)

## Installation

1. **Clone the repository:**

   ```bash
   git clone https://gitlab.softyn.com/web/zurno-api
   cd zurno-api
   ```

2. **Install dependencies**

   ```bash
   npm install
   or
   yarn install
   ```

3. **Copy env file**

   ```bash
   cp .env.example .env
   ```

4. **Usage**
   ```bash
   adonis serve --dev
   or
   npm run dev
   or
   yarn dev
   ```
5. **Initialize Database**

   ```bash
   node ace migration:fresh

   node ace db:seed

   node ace permission:fresh
   ```

## Permissions

This application use RBAC to manage permissions, which are stored in database.

### Initialize

Prepare the initial permissions using the following command.

```bash
node ace permission:fresh
```

This command drop all records in the permissions table, then runs all files in the <code>database/permissions</code> directory.

### Usage

```
import { HttpContext } from '@adonisjs/core/http'

export default class PostController {
   async create({ bouncer }: HttpContext) {
      await bouncer.authorize('allow', "create:post")

      // continue with controller logic
   }
}
```

### Adding permissions

New permissions can be created using the following command.

```bash
node ace make:permission new_permission

# create database/permissions/1731422153245_new_permission.ts
```

This command creates a new file inside the <code>database/permissions</code> director, prefixed with the current timestamp so that the files are sorted in the order created, and with the following content.

```
import AuthorizationService from '#services/authorization_service'

export default class {
   async up() {
      await AuthorizationService.createPermission("action", "resource")
   }

   async down() {
      await AuthorizationService.deletePermission("action", "resource")
   }
}
```

- The <code>up</code> method is used to modify the permissions table. Usually, you will create new permission inside this method.
- The <code>down</code> method is used to roll back the actions executed by the up method. For example, if the up method creates a permission, the down method should delete the same permission.

### Operations

**Run**  
 Once you have created the files you need, you can run the following command to proceed.

```bash
node ace permission:run
```

This command executes the <code>up</code> method on all files created **after** the latest file on record.

**Status**  
 Executed files are stored inside the <code>zn_permissions_control</code>, and can be checked using the following command

```bash
node ace permission:status
```

**Rollback**  
 You can roll back by running the <code>permission:rollback</code> command. The rollback action is performed on the files from the most recent batch. However, you can also specify a custom batch number where you want to roll back.

```bash
# Rollback to the previous batch
node ace migration:rollback

# Rollback to the start
node ace migration:rollback 0

# Rollback to batch 1
node ace migration:rollback 1
```

The rollback command executes the <code>down</code> method on permission files in reversed order.

**Reset**

```bash
node ace permission:reset
```

This command executes the <code>down</code> method on all files created **before** the lastest recorded file in reversed order.

## AI Search

```bash
run sql file from database/create_function_cosine_similarity.sql
```

## Streaming

This project makes use of IVS services from Amazon to create streams.

### IVS Player

In order to receive stream information, see [Amazon IVS Player](https://docs.aws.amazon.com/ivs/latest/LowLatencyUserGuide/player.html) documents.

The player also has the capability to receive [metadata](https://docs.aws.amazon.com/ivs/latest/LowLatencyUserGuide/metadata.html).

The metadata sent is freeform, so data from Zurno will generally look like:

``` js
{
   event: STREAM_EVENT,
   timestamp: 1740585121412,
   // other data
}
```

STREAM_EVENT types:

<code>variant.start</code>: Start showing Product Variant

Due to Amazon IVS metadata having a 1KB request limit (around 1000 characters), the product variant resource will be limited to important fields only.
``` js
{
   event: "variant.start",
   timestamp: 1740585121412,
   variant: {
        id: "VARIANT_UUID",
        shopifyVariantId: "gid://shopify/ProductVariant/SHOPIFY_VARIANT_ID",
        title: "VARIANT_TITLE",
        price: "VARIANT_PRICE",
        compareAtPrice: "VARIANT_COMPARE_AT_PRICE",
        product: {
            id: "PRODUCT_ID",
            title: "PRODUCT_TITLE",
            shopifyProductId: "gid://shopify/Product/SHOPIFY_PRODUCT_ID",
            reviewSummary: {
                averageRating: "PRODUCT_AVERAGE_RATING"
            }
        },
        image: {
            src: "IMAGE_SOURCE"
        }
    }
}
```
If the metadata exceeds the limit, it will be truncated to only the variant id:
``` js
{
   event: "variant.start",
   timestamp: 1740585121412,
   variant: {
      id: "VARIANT_UUID"
   }
}
```

<code>variant.end</code>: Stop showing Product Variant
``` js
{
   event: "variant.end",
   timestamp: 1740585121412,
   variant: {
      id: "VARIANT_UUID"
   }
}
```

<code>viewer.count</code>: Current viewer count
``` js
{
   event: "viewer.count",
   timestamp: 1740585121412,
   viewerCount: 100.
}
```


### IVS Chat

The project makes use of [Amazon IVS Chat](https://docs.aws.amazon.com/ivs/latest/ChatUserGuide/what-is.html) in order to create a live chat room.






## Live-Shopping Chat-Bot

### 1. What it is

A **multi-agent OpenAI-powered assistant** that answers customer queries in real time during live streams and in regular chat rooms.
The Orchestrator chooses one of four domain agents:

| Role (`EAIAssistantRole`) | Answers about…                              |
| ------------------------- | ------------------------------------------- |
| `SHOPPING_ASSISTANT`      | products, collections, sizing, availability |
| `POST_ASSISTANT`          | classifieds (jobs, salon sales, supplies…)  |
| `ORDER_ASSISTANT`         | order status, returns, shipping             |
| `CUSTOMER_SERVICE`        | site issues, feedback, general help         |

All agents subclass `BaseChatbotAgent`; they share the same call-chain and token-budget logic.

---

### 2. Prerequisites – **.env** keys

```env
# LLM + vector search
OPENAI_API_KEY=sk-***********************
PINECONE_API_KEY=***************

# Embedding indexes (one per service)
PROD_EMBEDDING_INDEX_NAME=
COLLECTION_EMBEDDING_INDEX_NAME=
POST_EMBEDDING_INDEX_NAME=

# OpenAI Assistant IDs – one per role
SHOPPING_ASSISTANT=asst_****************
POST_ASSISTANT=asst_****************
ORDER_ASSISTANT=asst_****************
CUSTOMER_SERVICE=asst_****************

# Cron toggles
CRON_JOB_ENABLE=true
```

---

### 3. First-time setup

```bash

node ace download:msds_links && node ace download:all-articles
node ace update:vector-database   # or   -t products,collections

# 3 ─ Push agent prompts / tool schemas to OpenAI
node ace update:setup_ai_assistant
```


---



### 5  Full request lifecycle 

```
┌─────────┐
│  USER   │ ①  HTTP POST /chat   { roomId, message }
└────┬────┘
     ▼
┌────────────────────────────────────────────────┐
│  ZurnoAssistantService                         │
│  ├─ previousAgent? ── yes ──► use same agent   │
│  │                         (skip routing)      │
│  └─ no ────────────►  OrchestratorRoute()      │
│                           tiny LLM call        │
│                           returns {"agent":X}  │
└────┬────────────────────────────────────────────┘
     ▼
┌──────────────────────────────────┐
│  AgentInstance  (X)              │
│  ├─ needsPreContext?             │
│  │     ├─ yes → getPreContext()  │ ⑤  e.g. Pinecone, SQL
│  │     └─ no                     │
│  └─ buildPrompt + history        │
│       ▼                          │
│     OpenAI Assistant             │ ⑥  returns answer + fn-calls
└────┬─────────────────────────────┘
     ▼
┌───────────────────────────────┐
│ agent.exitConversation?       │
│  ├─ false  → keep agent       │
│  └─ true   → OrchestratorRoute│ ⑦  (pick better agent)
└────┬──────────────────────────┘
     ▼
┌───────────────────────────────┐
│ postProcessNeeded?            │
│  ├─ yes → runToolCalls()      │ ⑧  product-search, post-create…
│  │          (may emit 2nd msg)│
│  └─ no                        │
└────┬──────────────────────────┘
     ▼
⑨  save reply → DB  |  emit via WebSocket  |  HTTP JSON back to USER
```

#### Step-by-step notes

| #     | Phase               | Details                                                                                                                                                                          |
| ----- | ------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **①** | *Entry*             | Client sends `{roomId, message}`.                                                                                                                                                |
| **②** | *Agent choice*      | If we already know the last agent for this room, reuse it; otherwise the **OrchestratorRoute** prompt (cheap chat-completion) returns `{"agent":"SHOPPING_ASSISTANT"}` etc.      |
| **③** | *Pre-context check* | Some agents (Shopping, Post) need extra data **before** calling the LLM—e.g. nearest product vectors from Pinecone or the user’s draft-post state.                               |
| **④** | *LLM call*          | Agent builds a full prompt: role system-msg + trimmed history + optional JSON pre-context + current user message. Sent to the agent’s **OpenAI Assistant**.                      |
| **⑤** | *Exit flag*         | Model can respond with `exitConversation=true` (via function call). Service then re-invokes OrchestratorRoute to pick a more suitable agent.                                     |
| **⑥** | *Post-processing*   | If the answer contains OpenAI function calls—`post_search`, `lookupProduct`, `createPost`—the agent executes them and either **patches** the reply or emits a follow-up message. |
| **⑦** | *Safety net*        | If routing cycles back to the same agent twice without success, the flow drops to **CUSTOMER\_SERVICE**.                                                                         |
| **⑧** | *Persistence*       | Final text is stored in `zn_chat_messages` and pushed over LiveSocket to the room.                                                                                               |
| **⑨** | *Second message*    | When a tool call generates additional user-visible output (e.g. search hits, draft confirmation), the agent sends a **second** assistant message after the tool finishes.        |



If an agent returns `{needsSecond:true}`, control loops back to the Orchestrator with `nextAgent`.

---

### 6. Extending with a new agent

1. Create `MyNewAgent extends BaseChatbotAgent` and implement `buildPrompt()` + `processTools()`.
2. Add `MY_NEW_AGENT` to `EAIAssistantRole` **and** `.env`.
3. Register it in `ZurnoAssistantService.makeAgent()` and update the **orchestrator prompt** routing rules.
4. Run `node ace update:setup_ai_assistant` to push the prompt/tool schema.
5. (Optional) If it needs vector search, add an embedding service and `.env` key, then populate via `update:vector-database`.

---

With these pieces in place, the chatbot handles everything from vector search and OpenAI function calls to RBAC—ready for live-shopping at scale.

### 7  Recommendation Service (vector-based)

Zurno’s product suggestions in chat and on the storefront are powered by a **lightweight vector-search layer** that ranks both individual SKUs and curated collections.

| Layer             | Responsibility                                                                                                                         | Key classes                                                                                                             |
| ----------------- | -------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------- |
| **Embedding**     | Turns product, collection, and post descriptions into 2 × 768-d OpenAI embeddings and upserts them into **Pinecone**.                  | `ProductEmbeddingService`, `CollectionEmbeddingService`, `PostEmbeddingService` (all extend `PineconeEmbeddingService`) |
| **Retrieval**     | Finds the K nearest neighbours for an arbitrary text query.                                                                            | `ProductRecommender`, `CollectionRecommender`                                                                           |
| **Fit scoring**   | Merges the two result lists, applies user constraints (price band, in-stock) and diversity heuristics, then assigns a composite score. | `RecommendationFitEvaluator`                                                                                            |
| **Orchestration** | Exposes a single method `recommend(query, constraints)` that returns up to five IDs (`productIds`, `collectionIds`).                   | `RecommendationEngine`                                                                                                  |

#### How it’s used
* **ShoppingAgent** – when a user asks “Do you have cozy hoodies under \$50?”, the agent calls

  ```ts
  const picks = this.engine.recommend(
      { productDescriptions: userMessage },
      { price_upper: 50, inStockOnly: 1 }
  )
  ```

  and injects the fetched product JSON into the prompt before the LLM call.

* **POST\_ASSISTANT** – if a classified author types “Looking for pink chrome powder”, the engine is queried in the background so the assistant can suggest matching supply listings.

#### Updating vectors

Run the Ace task whenever catalogue text changes:

```bash
node ace update:vector-database -t products,collections
```

This script:

1. Streams all active rows from MySQL.
2. Generates embeddings in batches of 100 via `openai.embeddings.create`.
3. Upserts into the Pinecone index whose name is defined in `.env`
   (`PROD_EMBEDDING_INDEX_NAME`, `COLLECTION_EMBEDDING_INDEX_NAME`, …).

> **Tip:** pair the command with a nightly cron job so new merchandise is searchable the next morning.

#### Tuning knobs (all optional)

| Constraint key               | Effect                                                                                               |
| ---------------------------- | ---------------------------------------------------------------------------------------------------- |
| `price_lower`, `price_upper` | Min / max unit price (USD)                                                                           |
| `inStockOnly` (`0/1`)        | Filter out OOS variants                                                                              |
| `MAX_TOTAL` (const)          | Maximum returned IDs (default = 5)                                                                   |
| `quota(nDesc)`               | Allocation rule that balances results when both product **and** collection descriptions are provided |



---



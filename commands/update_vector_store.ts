import UpdateVectorStoreJob from '#jobs/update_vector_store_job';
import { args, BaseCommand } from '@adonisjs/core/ace';
import type { CommandOptions } from '@adonisjs/core/types/ace';
import queue from '@rlanz/bull-queue/services/main';


export default class UpdateVectorStore extends BaseCommand {
  static commandName = 'update:vector-store'
  static description = 'Update OpenAI Vector Store'

  @args.string({
    required: true,
    description: 'OpenAI Vector Store ID'
  })
  declare openaiVectorStoreId: string

  static options: CommandOptions = {
    startApp: true,
  }

  async run() {

    try {
      const vectorStoreId = this.openaiVectorStoreId

      if (!vectorStoreId) {
        return this.logger.error("No Vector Store Id Found!");
      }

      await queue.dispatch(
        UpdateVectorStoreJob,
        { vectorStoreId },
        { queueName: 'syncData' }
      )

      this.logger.info('UpdateVectorStoreJob Sent')

    } catch (error) {
      console.log(error);
    }
  }
}

import ZnStream from '#models/zn_stream';
import { args, BaseCommand, flags } from '@adonisjs/core/ace';
import type { CommandOptions } from '@adonisjs/core/types/ace';
import {NOTIFICATION_TYPE} from "#constants/notification";
import { NotificationService } from '#services/notification_service';

export default class NotifyStream extends BaseCommand {
  static commandName = 'notify:stream'
  static description = 'Send out notifications for live stream'

  static options: CommandOptions = {
    startApp: true,
  }

  @args.string({
    required: false,
    description: "ID of Stream, default argument, not required if postId is used"
  })
  declare argsStreamId: string

  @flags.string({
    flagName: 'postId',
    description: "ID of Post, in case ID of stream is unknown"
  })
  declare flagPostId: string

  async run() {
    try {
      let stream
      switch (false) {
        case (!this.argsStreamId): {
          stream = await ZnStream.find(this.argsStreamId)
          break
        }
        case (!this.flagPostId): {
          stream = await ZnStream.findBy({ postId: this.flagPostId })
          break
        }
        default: {
          return this.logger.error('Stream or Post Id required.')
        }
      }
      if(stream) {
        const notificationService = new NotificationService()
        const notificationDataOfStreamStart = {
          title: "Zurno Live Now!",
          description: stream.title,
          type: NOTIFICATION_TYPE.STREAM_START,
          resourceId: stream.postId,
        }
        await notificationService.sendCampaign(notificationDataOfStreamStart)
      }

      return this.logger.info('Stream Notifications Sending.')

    } catch (error) {
      console.log(error)
    }
  }
}


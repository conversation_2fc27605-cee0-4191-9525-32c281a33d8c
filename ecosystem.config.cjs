module.exports = {
  apps: [
    {
      name: 'zurno-api',
      script: 'yarn',
      args: 'start', // Run both serve and queue:listen via npm script
      instances: 'max', // Auto-detect CPUs
      watch: false,
      ignore_watch: ['public'],
      exec_mode: 'cluster', // Cluster mode for better performance
      max_memory_restart: '300M', // Restart if memory exceeds 300MB
      env: {
        NODE_ENV: 'production',
      },
    },
    {
      name: 'zurno-queue',
      script: 'yarn',
      args: 'queue', // Run both serve and queue:listen via npm script
      instances: 'max', // Auto-detect CPUs
      watch: false,
      ignore_watch: ['public'],
      exec_mode: 'cluster', // Cluster mode for better performance
      max_memory_restart: '300M',
      env: {
        NODE_ENV: 'production',
      },
    },
  ],
}

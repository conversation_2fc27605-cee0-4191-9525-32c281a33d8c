import VendorService from "#services/vendors/vendor_service";
import { HttpContext } from "@adonisjs/core/http";
import VendorCommissionService from "#services/vendors/vendor_commission_service";

export default class VendorCommissionController {
  private vendorService: VendorService;
  private vendorCommissionService: VendorCommissionService;

  constructor() {
    this.vendorService = new VendorService();
    this.vendorCommissionService = new VendorCommissionService();
  }

  async index({ auth, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser
      console.log('user:', user);

      const vendor = await this.vendorService.getVendorByUserId(user.id);
      console.log('vendor:', vendor);

      const commissions = await this.vendorCommissionService.getAllCommisisonsByVendor(vendor.id);

      return response.ok(commissions);
    } catch (error) {
      console.log(error);
      return response.badRequest(error);
    }
  }

  async show({ auth, params, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser

      const commissionId = params.id;
      if (!commissionId) {
        return response.badRequest('Commission ID is required.');
      }

      const commission = await this.vendorCommissionService.getCommissionById(commissionId);

      return response.ok(commission);
    } catch (error) {
      console.log(error);
      return response.badRequest(error);
    }
  }
}
import ZnProduct from '#models/zn_product'
import ZnProductVariant from '#models/zn_product_variant'
import { ILineItem } from '#services/shopify/order/draft_order'
import { DraftOrderService } from '#services/shopify/order/draft_order_service'
import { ShopifyService } from '#services/shopify/shopify_service'
import { checkoutValidator } from '#validators/app/checkout/checkout_validator'
import type { HttpContext } from '@adonisjs/core/http'
import { getShopifyVariantId } from '../../../services/commons.js'
export default class CheckoutsController {
  private draftOrderService: DraftOrderService

  constructor() {
    this.draftOrderService = new DraftOrderService()
  }

  async create(ctx: HttpContext) {
    const { request, response, user } = ctx
    if (!user) {
      return response.status(401).json({ message: 'Unauthorized' })
    }
// In your controller:
    const rawPayload = request.all()

    if (Array.isArray(rawPayload.discountCodes)) {
      rawPayload.discountCodes = rawPayload.discountCodes.filter(
        (v) => typeof v === 'string' && v.trim() !== ''
      )
    }
    const { discountCodes, note, shippingAddress, appVersion, os, draftOrderId, ...payload } =
      await request.validateUsing(checkoutValidator, rawPayload)

    try {
      const tags = ['Zurno App', appVersion || '', os || ''].filter(Boolean)
      let order: any = null
      if (!draftOrderId) {
        const lineItems = [] as ILineItem[]
        // const isBundleOrder = !payload.lineItems.find((i) => !i.fastBundleDiscountId)

        for (const item of payload.lineItems) {
          if (!item.shopifyVariantId) {
            const variant = await ZnProductVariant.findOrFail(item.variantId)
            item.shopifyVariantId = variant.shopifyVariantId
          }
          const newItem = {
            quantity: item.quantity,
            variantId: item.shopifyVariantId,
            appliedDiscount: undefined,
          } as ILineItem

          // Apply the discount to the line item
          // @ts-ignore
          newItem.appliedDiscount = await this.draftOrderService.applyDiscount(
            item.shopifyVariantId,
            item.fastBundleDiscountId,
            item.discount
          )

          lineItems.push(newItem)
        }

        //create draft order
        order = await this.draftOrderService.create({
          user,
          shippingAddress,
          billingAddress: shippingAddress,
          lineItems,
          discountCodes,
          note,
          tags
        })
      } else {
        //update draft order
        order = await this.draftOrderService.updateDraftOrderTags({
          draftOrderId,
          tags,
          note,
        })
      }

      const url = order?.invoiceUrl || order?.checkoutUrl
      if (!url) {
        return response.badRequest({ message: 'Checkout failed' })
      }

      return response.ok({ url })
    } catch (error) {
      console.log(error.messages)
      return response.badRequest(error)
    }
  }

  async getProductsByVariantIds({ request, response }: HttpContext) {
    const { variantIds } = request.qs()

    if (!variantIds) {
      return response.ok([])
    }

    const shopifyVariantIds = variantIds.map((id: string) =>
      id.length <= 14 ? getShopifyVariantId(id) : id
    )

    const shopifyAdminAPI = new ShopifyService()
    const znVariants = await ZnProductVariant.query()
      .whereIn('shopifyVariantId', shopifyVariantIds)
      .orWhereIn('id', shopifyVariantIds)
    const { variants } = await shopifyAdminAPI.fetchVariantsWithIds(
      znVariants.map((v) => v.shopifyVariantId)
    )
    const znProducts = await ZnProduct.query()
      .whereIn(
        'shopifyProductId',
        variants.map((variant: any) => variant.product.id)
      )
      .preload('image')
    const products = []

    for (const variant of variants) {
      const product = variant.product
      const znProduct = znProducts.find((p) => p.shopifyProductId === product.id)
      product.zurnoId = znProduct?.id
      delete variant.product
      variant.image = variant.image || znProduct?.image

      const znVariant = znVariants.find((v) => v.shopifyVariantId === variant.id)
      variant.zurnoId = znVariant?.id
      products.push({ ...product, variant })
    }

    return response.ok(products)
  }
}

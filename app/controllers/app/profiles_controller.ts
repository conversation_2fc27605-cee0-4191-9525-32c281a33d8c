import { MEDIA_TYPE } from '#constants/media'
import Sync<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '#jobs/sync_user_address_job'
import ZnAddress from '#models/zn_address'
import ZnMedia from '#models/zn_media'
import ZnPost from '#models/zn_post'
import ZnPostCategory from '#models/zn_post_category'
import ZnUser from '#models/zn_user'
import { GeoService } from '#services/google/geo_service'
import ReportService from '#services/report_service'
import { SettingService } from '#services/setting_service'
import { ShopifyService } from '#services/shopify/shopify_service'
import { SmileRewardService } from '#services/smile_reward_service'
import {
  appCreateAddressValidator,
  setDefaultAddressValidator,
} from '#validators/app/address/address_validator'
import { updateLocaleValidator } from '#validators/app/profile/profile.validator'
import type { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import queue from '@rlanz/bull-queue/services/main'
import { DateTime } from 'luxon'
import { AuthService } from '../../../services/auth/auth_service.js'
import { AmazonS3StorageService } from '../../../services/aws/s3/aws-s3.service.js'
import { getShopifyCustomerId } from '../../../services/commons.js'
import { ShopifyAuthService } from '../../../services/shopify/auth/shopify_auth_service.js'

export default class ProfilesController {
  private shopifyAuthService: ShopifyAuthService
  private rewardService: SmileRewardService
  private amazonS3StorageService: AmazonS3StorageService
  private settingService: SettingService
  constructor() {
    this.shopifyAuthService = new ShopifyAuthService()
    this.rewardService = new SmileRewardService()
    this.amazonS3StorageService = new AmazonS3StorageService()
    this.settingService = new SettingService()
  }

  /**
   * @getProfile
   * @tag Profiles
   * @summary Get profile of current user
   * @responseBody 200 - <ZnUser>.append("points":42) - Get profile of current user based on bearer token
   */
  async getProfile({ response, request, auth }: HttpContext) {
    const { latitude = null, longitude = null } = request.qs()
    const user = auth.getUserOrFail() as ZnUser
    await user.load('defaultAddress')

    let shopify
    if (user.shopifyCustomerId) {
      shopify = await this.shopifyAuthService.getCustomerById(user.shopifyCustomerId)
      if (shopify) {
        const shopifyService = new ShopifyService()

        const rawAddresses = await shopifyService.getCustomerAddress(user.shopifyCustomerId)

        const userAddresses = (
          await ZnAddress.query().where({ userId: user.id }).whereNull('deletedAt')
        )
          .map((a) =>
            a.shopifyId
              ? `gid://shopify/MailingAddress/${a.shopifyId}?model_name=CustomerAddress`
              : null
          )
          .filter(Boolean)

        shopify.addresses = rawAddresses?.filter((a: any) => userAddresses?.includes(a.id))
        shopify.default_address = shopify.default_address
          ? {
              ...shopify.default_address,
              id: `gid://shopify/MailingAddress/${shopify.default_address.id}?model_name=CustomerAddress`,
            }
          : null
        const { data } = await this.rewardService.fetchCustomerData(user.email)
        shopify.points = data?.customers[0]?.points_balance || 0
        // in case where db has phone but shopify has not
        user.phone = shopify.phone || user.phone
        user.shopifyCustomerId = shopify.id
        user.rewardPoints = shopify.points
        user.smileId = data?.customers[0]?.id
      }
    }

    //Get customer's discounts
    // const discounts = await this.rewardService.getCustomerDiscounts(user)
    // user.noDiscount = Object.values(discounts.val() ?? [])?.length ?? 0
    //Update user

    if (latitude && longitude) {
      user.latitude = latitude
      user.longitude = longitude
    }
    await user.save()

    await this.settingService.createOrFindByUserId(user.id)
    await user.load('setting')

    return response.ok({
      user: { ...user?.serialize(), points: shopify?.points },
      shopify,
    })
  }

  /**
   * @updateProfile
   * @tag Profiles
   * @summary Update user profile
   * @description Update user profile information
   * @requestBody <ZnUser>.only(firstName,lastName,phone,email,latitude,longitude,mile,timezone).append("file":"","cover":"","gender":"male|female","birthday":"1970-01-01")
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async updateProfile({ request, response, auth }: HttpContext) {
    try {
      let avatarUrl = ''
      let avatarMediaDto
      const uploadPromises: any[] = []
      //Upload avatar
      request.multipart?.onFile('file', {}, async (part) => {
        const chunks: any[] = []

        // Collect the file data from the stream
        part.on('data', (chunk) => {
          chunks.push(chunk)
        })

        part.on('end', async () => {
          const buffer = Buffer.concat(chunks)
          const ext = part.filename.match(/(\.[a-zA-Z0-9]+)$/)
          const transformedFile = {
            fileName: part.filename as string,
            buffer,
            mimeType: part.headers['content-type'],
            extname: ext ? ext[0] : '',
          }

          // Upload to S3 and store avatar URL
          uploadPromises.push(
            this.amazonS3StorageService.uploadImages([transformedFile]).then((uploadedToS3) => {
              if (
                uploadedToS3 &&
                uploadedToS3.uploadedFiles &&
                uploadedToS3.uploadedFiles.length > 0
              ) {
                avatarUrl = uploadedToS3.uploadedFiles[0].fileKey
                avatarMediaDto = uploadedToS3.uploadedFiles[0]
              }
            })
          )
        })

        part.on('error', (error) => {
          console.error(`Failed to process file: ${error.message}`)
        })
      })

      let coverId = ''
      // Upload cover
      request.multipart?.onFile('cover', {}, async (part) => {
        const chunks: any[] = []

        // Collect the file data from the stream
        part.on('data', (chunk) => {
          chunks.push(chunk)
        })

        part.on('end', async () => {
          const buffer = Buffer.concat(chunks)
          const ext = part.filename.match(/(\.[a-zA-Z0-9]+)$/)
          const transformedFile = {
            fileName: part.filename as string,
            buffer,
            mimeType: part.headers['content-type'],
            extname: ext ? ext[0] : '',
          }

          // Upload to S3 and store cover to znmedia
          uploadPromises.push(
            this.amazonS3StorageService
              .uploadImages([transformedFile])
              .then(async (uploadedToS3) => {
                if (
                  uploadedToS3 &&
                  uploadedToS3.uploadedFiles &&
                  uploadedToS3.uploadedFiles.length > 0
                ) {
                  const cover = await ZnMedia.create({
                    fileKey: uploadedToS3.uploadedFiles[0].fileKey,
                    url: uploadedToS3.uploadedFiles[0].fileKey,
                    type: uploadedToS3.uploadedFiles[0].fileType as MEDIA_TYPE,
                  })

                  coverId = cover.id
                }
              })
          )
        })

        part.on('error', (error) => {
          console.error(`Failed to process file: ${error.message}`)
        })
      })

      await request.multipart?.process()

      const formData = request.all()
      // @ts-ignore
      const currentUser = auth.getUserOrFail() as ZnUser

      //Update shopify
      const { success, user: shopifyUser } = await this.shopifyAuthService.updateCustomer(
        currentUser.shopifyCustomerId,
        {
          id: currentUser.shopifyCustomerId,
          first_name: formData.firstName,
          last_name: formData.lastName,
          phone: formData.phone,
        }
      )

      // for cases when phone is not intended to be updated
      formData.phone = formData.phone || currentUser.phone

      if (success && shopifyUser && shopifyUser.phone === formData.phone) {
        // Await all uploads to complete
        await Promise.all(uploadPromises)

        if (avatarUrl) {
          currentUser.avatar = avatarUrl
        }
        if (avatarMediaDto) {
          const avatarMedia = await ZnMedia.create({
            fileKey: avatarMediaDto['fileKey'],
            url: avatarMediaDto['fileKey'],
            type: avatarMediaDto['fileType'],
          })
          currentUser.avatarId = avatarMedia.id
        }
        if (coverId) {
          currentUser.coverId = coverId
        }

        //Update local db
        currentUser.firstName = formData?.firstName ?? currentUser.firstName
        currentUser.lastName = formData?.lastName ?? currentUser.lastName
        currentUser.phone = formData?.phone ?? currentUser.phone
        currentUser.gender = formData?.gender ?? currentUser.gender
        // currentUser.latitude = formData?.latitude ?? currentUser.latitude
        // currentUser.longitude = formData?.longitude ?? currentUser.longitude
        currentUser.mile = formData?.mile ?? currentUser.mile
        currentUser.shareUrl = formData?.shareUrl ?? currentUser.shareUrl
        if (formData.timezone) {
          currentUser.timezone = formData.timezone
        }
        if (formData.birthday) {
          currentUser.birthday = DateTime.fromISO(
            new Date(formData.birthday.toString()).toISOString()
          )
        }

        // check if there's new lat and lon
        if (formData.latitude && formData.longitude) {
          // check if lat and lon are different
          if (
            formData.latitude != currentUser.latitude ||
            formData.longitude != currentUser.longitude
          ) {
            let newAddress

            const geoService = new GeoService()
            const address = await geoService.getAddressFromLatLon(
              formData.latitude,
              formData.longitude
            )

            const formattedAddress = {
              firstName: currentUser.firstName,
              lastName: currentUser.lastName,
              phone: currentUser.phone || '',

              address1: address.address1,
              city: address.city,
              province: address.province,
              country: address.country,
              zip: address.zip,
            }

            if (currentUser.shopifyCustomerId) {
              const shopifyService = new ShopifyService()

              const shopifyAddresses = await shopifyService.getCustomerAddress(
                currentUser.shopifyCustomerId
              )

              const shopifyAddress = await shopifyService.createOrUpdateCustomerAddress({
                id: getShopifyCustomerId(currentUser.shopifyCustomerId),
                addresses: [formattedAddress, ...shopifyAddresses],
              })

              // get new address from shopify and update db
              if (shopifyAddress?.addresses) {
                const newShopifyAddress = shopifyAddress.addresses.pop()

                if (newShopifyAddress) {
                  const indexOfQuestionMark = newShopifyAddress.id.indexOf('?')
                  const shopifyAddressId = newShopifyAddress.id.slice(0, indexOfQuestionMark)
                  const shopifyAddressIdArray = shopifyAddressId.split('/')
                  const shopifyId = shopifyAddressIdArray[shopifyAddressIdArray.length - 1]

                  if (shopifyId) {
                    await shopifyService.setDefaultCustomerAddress({
                      customerId: currentUser.shopifyCustomerId,
                      addressId: newShopifyAddress.id,
                    })

                    const addressData = {
                      shopifyId,

                      firstName: newShopifyAddress.firstName,
                      lastName: newShopifyAddress.lastName,
                      name: newShopifyAddress.name,
                      phone: newShopifyAddress.phone,
                      company: newShopifyAddress.company,

                      address1: newShopifyAddress.address1,
                      address2: newShopifyAddress.address2,
                      city: newShopifyAddress.city,
                      province: newShopifyAddress.province,
                      country: newShopifyAddress.country,
                      zip: newShopifyAddress.zip,
                      provinceCode: newShopifyAddress.provinceCode,
                      countryCode: newShopifyAddress.countryCode,

                      latitude: formData.latitude,
                      longitude: formData.longitude,

                      userId: currentUser.id,

                      isDefault: true,
                    }

                    const authService = new AuthService()
                    newAddress = await authService.createAddress(addressData)
                  }
                }
              }
            }

            // save lat lot, set default address
            currentUser.latitude = formData.latitude
            currentUser.longitude = formData.longitude
            currentUser.defaultAddressId = newAddress?.id || currentUser.defaultAddressId
          }
        }

        await currentUser.save()

        // Reload user with cover relationship
        // @ts-ignore
        const updatedUser = await ZnUser.query({ mode: 'write' })
          .where('id', currentUser.id)
          .preload('cover')
          .preload('avatarMedia')
          .preload('defaultAddress')
          .firstOrFail()

        // Serialize user data and add coverUrl
        const userData = updatedUser.serialize()

        if (updatedUser.cover?.url) {
          const isAbsoluteUrl =
            updatedUser.cover.url.startsWith('http://') ||
            updatedUser.cover.url.startsWith('https://')
          userData.coverUrl = isAbsoluteUrl
            ? updatedUser.cover.url
            : this.amazonS3StorageService.getFullUrl() + updatedUser.cover.url
        }

        return response.ok({
          message: 'Profile updated successfully.',
          user: userData,
          shopify: shopifyUser,
        })
      } else {
        return response.badRequest({
          message: 'Profile update failed. Shopify update unsuccessful or phone not updated.',
          shopifyPhone: shopifyUser?.phone,
          requestedPhone: formData.phone,
        })
      }
    } catch (error) {
      console.log(error)
      return response.badRequest({
        message: 'Profile update failed.',
        error: error?.message || error,
      })
    }
  }

  /**
   * @updateLocale
   * @tag Profiles
   * @summary Update user locale
   * @requestBody {"locale": "en"}
   */
  async updateLocale({ request, response, user }: HttpContext) {
    const { locale } = await request.validateUsing(updateLocaleValidator)
    if (!user) {
      return response.forbidden({ message: 'Access denied' })
    }
    await user.merge({ locale }).save()
    return response.ok(user)
  }

  /**
   * @destroyProfile
   * @tag Profiles
   * @summary Destroy user profile
   */
  async destroyProfile({ auth, response }: HttpContext) {
    const currentUser = auth.getUserOrFail() as ZnUser
    if (currentUser) {
      await currentUser.softDelete()
      return response.ok('Deleted')
    }
    return response.badRequest('Something went wrong!')
  }

  /**
   * @report
   * @tag Profiles
   * @summary Report
   */
  public async report({ request, response, user }: HttpContext) {
    if (!user) {
      return response.forbidden({ message: 'Access denied' })
    }
    const payload = request.qs()
    const endDate =
      DateTime.fromFormat(payload?.endDate, 'yyyy-MM-dd') ||
      DateTime.now().minus({ days: 1 }).endOf('day')
    const startDate =
      DateTime.fromFormat(payload?.startDate, 'yyyy-MM-dd') ||
      endDate.minus({ days: 6 }).startOf('day')

    const reportService = new ReportService(startDate, endDate, user.id)
    const report = await reportService.getDetailedReport()

    return response.ok(report)
  }

  public async getAddresses({ response, user }: HttpContext) {
    if (!user) {
      return response.forbidden({ message: 'Access denied' })
    }
    if (!user.shopifyCustomerId) {
      return response.ok([])
    }
    const shopifyService = new ShopifyService()
    const addresses = await shopifyService.getCustomerAddress(user.shopifyCustomerId)
    const userAddresses = (
      await ZnAddress.query().where({ userId: user.id }).whereNull('deletedAt')
    )
      .map((a) =>
        a.shopifyId
          ? `gid://shopify/MailingAddress/${a.shopifyId}?model_name=CustomerAddress`
          : null
      )
      .filter(Boolean)

    return response.ok(addresses?.filter((a: any) => userAddresses?.includes(a.id)))
  }

  public async getZurnoAddresses({ response, user }: HttpContext) {
    if (!user) {
      return response.forbidden({ message: 'Access denied' })
    }
    const addresses =
      (
        await ZnAddress.query()
          .where({ userId: user.id })
          .whereNotNull('shopifyId')
          .whereNull('deletedAt')
      )?.map((a) => ({
        ...a.toJSON(),
        default: a.id === user.defaultAddressId,
      })) || []

    return response.ok(addresses)
  }

  public async createAddresses({ request, response, user }: HttpContext) {
    if (!user) {
      return response.forbidden({ message: 'Access denied' })
    }
    if (!user.shopifyCustomerId) {
      return response.badRequest('Shopify customer ID not found')
    }
    const { isDefault, ...payload } = await request.validateUsing(appCreateAddressValidator)
    try {
      const shopifyService = new ShopifyService()
      const addresses = await shopifyService.getCustomerAddress(user.shopifyCustomerId)
      addresses.push(payload)
      const shopifyAddresses = await shopifyService.createOrUpdateCustomerAddress({
        id: getShopifyCustomerId(user.shopifyCustomerId),
        addresses,
      })
      queue.dispatch(SyncUserAddressJob, { userId: user.id }, { queueName: 'syncData' })

      return response.ok(shopifyAddresses)
    } catch (error) {
      return response.badRequest('Something went wrong')
    }
  }

  public async updateAddresses({ request, response, user, params }: HttpContext) {
    if (!user) {
      return response.forbidden({ message: 'Access denied' })
    }
    if (!user.shopifyCustomerId) {
      return response.badRequest('Shopify customer ID not found')
    }

    const { isDefault, ...payload } = await request.validateUsing(appCreateAddressValidator)
    const addressId = params.id
    const address = await ZnAddress.query().orWhere({ shopifyId: addressId }).first()
    try {
      const shopifyService = new ShopifyService()
      const rawAddresses = await shopifyService.getCustomerAddress(user.shopifyCustomerId)
      const addresses = rawAddresses.map((address: any) => {
        if (address.id.includes(addressId)) {
          return { ...payload, id: address.id }
        }
        return address
      })

      const shopifyAddresses = await shopifyService.createOrUpdateCustomerAddress({
        id: getShopifyCustomerId(user.shopifyCustomerId),
        addresses,
      })
      if (address) {
        await address.merge({ ...payload, shopifyId: addressId }).save()
      } else {
        await ZnAddress.create({ ...payload, shopifyId: addressId })
      }
      queue.dispatch(SyncUserAddressJob, { userId: user.id }, { queueName: 'syncData' })

      return response.ok(shopifyAddresses)
    } catch (error) {
      return response.badRequest('Something went wrong')
    }
  }

  public async deleteAddresses({ response, user, params }: HttpContext) {
    if (!user) {
      return response.forbidden({ message: 'Access denied' })
    }
    const addressId = params.id
    if (!user.shopifyCustomerId) {
      return response.badRequest('Shopify customer ID not found')
    }
    const address = await ZnAddress.query().orWhere({ shopifyId: addressId }).first()

    try {
      const shopifyService = new ShopifyService()
      const rawAddresses = await shopifyService.getCustomerAddress(user.shopifyCustomerId)
      const addresses = rawAddresses.filter((address: any) => {
        return !address.id.includes(addressId)
      })

      const shopifyAddresses = await shopifyService.createOrUpdateCustomerAddress({
        id: getShopifyCustomerId(user.shopifyCustomerId),
        addresses,
      })
      if (address) {
        await address.softDelete()
      }
      queue.dispatch(SyncUserAddressJob, { userId: user.id }, { queueName: 'syncData' })

      return response.ok(shopifyAddresses)
    } catch (error) {
      return response.badRequest('Something went wrong')
    }
  }

  public async setDefaultAddresses({ response, user, request }: HttpContext) {
    if (!user) {
      return response.forbidden({ message: 'Access denied' })
    }
    const { id: addressId } = await request.validateUsing(setDefaultAddressValidator)
    if (!user.shopifyCustomerId) {
      return response.badRequest('Shopify customer ID not found')
    }
    const address = await ZnAddress.query().orWhere({ shopifyId: addressId }).first()

    try {
      const shopifyService = new ShopifyService()
      const customer = await shopifyService.setDefaultCustomerAddress({
        customerId: user.shopifyCustomerId,
        addressId,
      })
      if (address) {
        await user.merge({ defaultAddressId: address.id }).save()
      }

      return response.ok(customer)
    } catch (error) {
      return response.badRequest('Something went wrong')
    }
  }

  /**
   * Get post action report (view, call, share) for a specific user
   */
  public async getUserPostReport({ request, response, auth }: HttpContext) {
    try {
      const { startISO, endISO, prevStartISO, prevEndISO } = this.getDateRanges(request.qs())

      // Get authenticated user
      const user = (auth.getUserOrFail() as ZnUser).serialize()

      if (!user?.id) {
        return response.unauthorized({ message: 'User not authenticated' })
      }

      const data = await this.fetchUserTrackingData(user.id, startISO, endISO)

      const prevData = await this.fetchUserTrackingData(user.id, prevStartISO, prevEndISO)

      const totalView = this.formatTotal(
        data.reduce((sum, item) => sum + Number(item.totalViews || 0), 0)
      )
      const totalCall = this.formatTotal(data.reduce((sum, item) => sum + item.totalCalls, 0))
      const totalShare = this.formatTotal(data.reduce((sum, item) => sum + item.totalShares, 0))

      const prevTotalView = this.formatTotal(
        prevData.reduce((sum, item) => sum + item.totalViews, 0)
      )
      const prevTotalCall = this.formatTotal(
        prevData.reduce((sum, item) => sum + item.totalCalls, 0)
      )
      const prevTotalShare = this.formatTotal(
        prevData.reduce((sum, item) => sum + item.totalShares, 0)
      )

      const topPosts = await this.topViewPostReport(user.id, startISO, endISO)

      return response.ok({
        view: {
          total: totalView,
          change: this.percentChange(totalView, prevTotalView),
          categories: this.calculateCategoryPercentage(data, 'totalViews', totalView),
        },
        call: {
          total: totalCall,
          change: this.percentChange(totalCall, prevTotalCall),
          categories: this.calculateCategoryPercentage(data, 'totalCalls', totalCall),
        },
        share: {
          total: totalShare,
          change: this.percentChange(totalShare, prevTotalShare),
          categories: this.calculateCategoryPercentage(data, 'totalShares', totalShare),
        },
        topPosts: topPosts,
      })
    } catch (error) {
      console.error('Error fetching user post report:', error)
      return response.status(500).send({
        message: 'An error occurred while fetching user post report',
        error: error.message,
      })
    }
  }

  /**
   * Fetch tracking data for a specific user
   */
  private async fetchUserTrackingData(userId: string, startDate?: string, endDate?: string) {
    const categories = await ZnPostCategory.query()
      .whereNull('parentId')
      .preload('thumbnail')
      .select()

    const query = db
      .from('zn_resource_interacts as ri')
      .join('zn_posts as posts', 'ri.resourceId', 'posts.id')
      .join('zn_posts_post_categories as post_categories', 'posts.id', 'post_categories.postId')
      .join('zn_post_categories as categories', 'post_categories.postCategoryId', 'categories.id')
      .select(
        'categories.id as categoryId',
        'categories.name as categoryName',
        db.raw('SUM(ri.viewCount) as totalViews'),
        db.raw('SUM(ri.clickCallOnCount) as totalCalls'),
        db.raw('SUM(ri.shareCount) as totalShares')
      )
      .where('posts.userId', userId)
      .andWhereNull('categories.parentId')
      .andWhereNull('posts.deletedAt')
      .andWhere('ri.resource', 'ZnPost')
      .groupBy('categories.id', 'categories.name')

    if (startDate) query.where('ri.createdAt', '>=', new Date(startDate))
    if (endDate) query.where('ri.createdAt', '<=', new Date(endDate))

    const categoryThumbnails = new Map(
      categories.map((cat) => [cat.id, cat.thumbnail ? cat.thumbnail.url : null])
    )

    const trackingData = await query

    return trackingData.map((item) => ({
      ...item,
      categoryThumbnail: categoryThumbnails.get(item.categoryId) || null,
    }))
  }

  /**
   * Get Top 10 Most Viewed Posts for a user
   */
  private async topViewPostReport(userId: string, startDate?: string, endDate?: string) {
    const topViewedPosts = await db
      .from('zn_resource_interacts as ri')
      .join('zn_posts as p', 'ri.resourceId', 'p.id')
      .select('ri.resourceId')
      .whereBetween('ri.createdAt', [new Date(startDate as any), new Date(endDate as any)])
      .andWhere('p.userId', userId)
      .andWhere('ri.resource', 'ZnPost')
      .orderBy('ri.viewCount', 'desc')
      .limit(10)

    const postIds = topViewedPosts.map((t) => t.resourceId)

    if (postIds.length === 0) {
      return []
    }

    const posts = await ZnPost.query()
      .whereIn('id', postIds)
      .preload('categories', (query) => query.preload('thumbnail'))
      .preload('medias')
      .preload('store')
      .preload('user')
      .preload('thumbnail')
      .preload('translations')

    return posts
  }

  /**
   * Calculate percentage contribution of each category
   */
  private calculateCategoryPercentage(data: any[], key: string, total: number) {
    return data
      .filter((item) => Number(item[key]) > 0)
      .map((item) => {
        const percentage = total > 0 ? ((Number(item[key]) / total) * 100).toFixed(2) : '0'
        return {
          category: item.categoryName,
          percentage: percentage === '0.00' ? '< 0.01' : percentage,
          count: Number(item[key]),
          categoryThumbnail: item.categoryThumbnail,
        }
      })
  }

  /**
   * Format total numbers (convert strings to numbers)
   */
  private formatTotal(value: any) {
    return Number(value) || 0
  }

  /**
   * Calculate percentage change between two values
   */
  private percentChange(current: number, previous: number) {
    return previous && previous > 0 ? ((current - previous) / previous) * 100 : 0
  }

  /**
   * Get Date Ranges for reporting
   */
  private getDateRanges(qs: any) {
    let { startDate, endDate } = qs

    if (!startDate || !endDate) {
      const today = new Date()
      const firstDayOfWeek = new Date(today.setDate(today.getDate() - today.getDay()))
      const lastDayOfWeek = new Date(firstDayOfWeek)
      lastDayOfWeek.setDate(firstDayOfWeek.getDate() + 6)

      startDate = firstDayOfWeek.toISOString().split('T')[0]
      endDate = lastDayOfWeek.toISOString().split('T')[0]
    }

    const startISO = new Date(startDate).toISOString()
    const endISO = new Date(endDate).toISOString()
    const duration =
      (new Date(endDate).getTime() - new Date(startDate).getTime()) / (1000 * 60 * 60 * 24) + 1
    const prevStartISO = new Date(
      new Date(startDate).getTime() - duration * 24 * 60 * 60 * 1000
    ).toISOString()
    const prevEndISO = new Date(
      new Date(endDate).getTime() - duration * 24 * 60 * 60 * 1000
    ).toISOString()

    return { startISO, endISO, prevStartISO, prevEndISO }
  }
}

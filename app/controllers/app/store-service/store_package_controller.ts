import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'
import ZnStorePackage from '#models/store_service/zn_store_package'
import ZnStoreService from '#models/store_service/zn_store_service'
import { createPackageValidator, updatePackageValidator } from '#validators/store-service/index'
import db from "@adonisjs/lucid/services/db";

@inject()
export default class StorePackageController {
  /**
   * @swagger
   * /api/v1/app/store-service/packages:
   *   get:
   *     tags:
   *       - Store Packages
   *     summary: List packages
   *     parameters:
   *       - name: storeId
   *         in: query
   *         required: true
   *         schema:
   *           type: string
   *       - name: page
   *         in: query
   *         schema:
   *           type: number
   *       - name: limit
   *         in: query
   *         schema:
   *           type: number
   *       - name: priceFrom
   *         in: query
   *         schema:
   *           type: number
   *       - name: priceTo
   *         in: query
   *         schema:
   *           type: number
   *       - name: durationFrom
   *         in: query
   *         schema:
   *           type: number
   *       - name: durationTo
   *         in: query
   *         schema:
   *           type: number
   */
  async index({ request, response }: HttpContext) {
    const {
      page = 1,
      limit = 10,
      storeId,
      priceFrom,
      priceTo,
      durationFrom,
      durationTo,
    } = request.qs()

    const query = ZnStorePackage.query()
      .where('storeId', storeId)
      .preload('image')
      .preload('services', (query) => {
        query.preload('image')
      })

    // Filter by total price
    if (priceFrom !== undefined || priceTo !== undefined) {
      query.whereExists((subQuery) => {
        subQuery
          .from('zn_store_package_services as ps')
          .join('zn_store_services as s', 's.id', 'ps.serviceId')
          .whereRaw('ps.packageId = zn_store_packages.id')
          .groupBy('ps.packageId', 'ps.customPrice', 'ps.serviceId')
          .having(
            db.raw('SUM(COALESCE(ps.customPrice, s.price))'),
            '>=',
            Number(priceFrom || 0)
          )
          .if(priceTo !== undefined, (query) => {
            query.having(
              db.raw('SUM(COALESCE(ps.customPrice, s.price))'),
              '<=',
              Number(priceTo!)
            )
          })
      })
    }

    // Filter by total duration
    if (durationFrom !== undefined || durationTo !== undefined) {
      query.whereExists((subQuery) => {
        subQuery
          .from('zn_store_package_services as ps')
          .join('zn_store_services as s', 's.id', 'ps.serviceId')
          .whereRaw('ps.packageId = zn_store_packages.id')
          .groupBy('ps.packageId', 'ps.customPrice', 'ps.serviceId')
          .having(
            db.raw('SUM(s.duration)'),
            '>=',
            Number(durationFrom || 0)
          )
          .if(durationTo !== undefined, (query) => {
            query.having(
              db.raw('SUM(s.duration)'),
              '<=',
              Number(durationTo!)
            )
          })
      })
    }

    interface CustomPriceMap {
      [packageId: string]: {
        [serviceId: string]: number
      }
    }

    const result = (await query.paginate(page, limit)).serialize()

    const packageIds = result.data.map((d) => d.id)
    const customPricesQuery = await db
      .from('zn_store_package_services')
      .whereIn('packageId', packageIds)
      .select('packageId', 'serviceId', 'customPrice')

    const customPricesMap: CustomPriceMap = {}
    customPricesQuery.forEach((row: any) => {
      if (!customPricesMap[row.packageId]) {
        customPricesMap[row.packageId] = {}
      }
      customPricesMap[row.packageId][row.serviceId] = row.customPrice
    })

    result.data = await Promise.all(
      result.data.map(async (package_: any) => {
        package_.services = await Promise.all(
          package_.services.map(async (service: any) => {
            const customPrice = customPricesMap[package_.id]?.[service.id]
            return {
              ...service,
              price: customPrice || service.price
            }
          })
        )
        return package_
      })
    )

    return response.ok(result)
  }

  /**
   * @swagger
   * /api/v1/app/store-service/packages:
   *   post:
   *     tags:
   *       - Store Packages
   *     summary: Create a new package
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *               - storeId
   *               - services
   *             properties:
   *               name:
   *                 type: string
   *               storeId:
   *                 type: string
   *               imageId:
   *                 type: string
   *               services:
   *                 type: array
   *                 items:
   *                   type: object
   *                   required:
   *                     - id
   *                   properties:
   *                     id:
   *                       type: string
   *                     customPrice:
   *                       type: number
   */
  async store({ request, response }: HttpContext) {
    const payload = await createPackageValidator.validate(request.all())

    const package_ = await ZnStorePackage.create({
      name: payload.name,
      storeId: payload.storeId,
      imageId: payload.imageId,
    })

    // Prepare pivot data with custom prices
    const pivotData = await Promise.all(
      payload.services.map(async (service) => {
        const originalService = await ZnStoreService.findOrFail(service.id)
        return {
          [service.id]: {
            customPrice: service.customPrice || originalService.price,
          },
        }
      })
    )

    // Attach services with custom prices
    await package_.related('services').attach(Object.assign({}, ...pivotData))

    await Promise.all([
      package_.load((loader) => {
        loader.load('services', (query) => {
          query.preload('image')
          query.pivotColumns(['customPrice'])
        })
      }),
      package_.load('image'),
    ])

    return response.ok(package_.serialize())
  }

  /**
   * @swagger
   * /api/v1/app/store-service/packages/{id}:
   *   put:
   *     tags:
   *       - Store Packages
   *     summary: Update a package
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: string
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *               imageId:
   *                 type: string
   *               services:
   *                 type: array
   *                 items:
   *                   type: object
   *                   required:
   *                     - id
   *                   properties:
   *                     id:
   *                       type: string
   *                     customPrice:
   *                       type: number
   */
  async update({ request, response, params }: HttpContext) {
    const payload = await updatePackageValidator.validate(request.all())
    const package_ = await ZnStorePackage.findOrFail(params.id)

    if (payload.name || payload.imageId) {
      await package_
        .merge({
          name: payload.name,
          imageId: payload.imageId,
        })
        .save()
    }

    if (payload.services) {
      // Prepare pivot data with custom prices
      const pivotData = await Promise.all(
        payload.services.map(async (service) => {
          const originalService = await ZnStoreService.findOrFail(service.id)
          return {
            [service.id]: {
              customPrice: service.customPrice || originalService.price,
            },
          }
        })
      )

      // Sync services with custom prices
      await package_.related('services').sync(Object.assign({}, ...pivotData))
    }

    await Promise.all([
      package_.load((loader) => {
        loader.load('services', (query) => {
          query.preload('image')
          query.pivotColumns(['customPrice'])
        })
      }),
      package_.load('image'),
    ])

    return response.ok(package_.serialize())
  }

  /**
   * @swagger
   * /api/v1/app/store-service/packages/{id}:
   *   delete:
   *     tags:
   *       - Store Packages
   *     summary: Delete a package
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: string
   */
  async destroy({ params, response }: HttpContext) {
    const package_ = await ZnStorePackage.findOrFail(params.id)
    await package_.softDelete()
    return response.ok({
      message: 'Package deleted successfully',
    })
  }
}

import { belongsTo, column, computed } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations';
import AppModel from './app_model.js';
import { EApprovalStatus } from '#constants/approval_status';
import ZnOrder from './zn_order.js';
import ZnVendor from './zn_vendor.js';

export default class ZnVendorCommission extends AppModel {
  @column({
    columnName: 'commissionRate',
    consume: (value: string) => parseFloat(value)
  })
  declare commissionRate: number

  @column({
    columnName: 'fixedCommissionAmount',
    consume: (value: string) => parseFloat(value)
  })
  declare fixedCommissionAmount: number

  @column({
    columnName: 'commissionAmount',
    consume: (value: string) => parseFloat(value)
  })
  declare commissionAmount: number

  @column({ columnName: 'status' })
  declare status: string | EApprovalStatus.PENDING

  @column({ columnName: 'rejectionReason' })
  declare rejectionReason: string | null

  @column({
    columnName: 'adjustedAmount',
    consume: (value: string) => parseFloat(value)
  })
  declare adjustedAmount: number

  @column({ columnName: 'adjustedReason' })
  declare adjustedReason: string

  @column({ columnName: 'vendorId' })
  declare vendorId: string

  @column({ columnName: 'orderId' })
  declare orderId: string

  @computed()
  get finalAmount() {
    if (this.adjustedAmount !== null && this.adjustedAmount > 0)
      return this.adjustedAmount
    return this.commissionAmount
  }

  @belongsTo(() => ZnVendor, {
    foreignKey: 'vendorId'
  })
  declare vendor: BelongsTo<typeof ZnVendor>

  @belongsTo(() => ZnOrder, {
    foreignKey: 'orderId'
  })
  declare order: BelongsTo<typeof ZnOrder>
}
import SendNotificationPostCommentJob, {
  CommentJobType,
} from '#jobs/send_notification_post_comment_job'
import { afterCreate, belongsTo, column, hasMany, manyToMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany, ManyToMany } from '@adonisjs/lucid/types/relations'
import queue from '@rlanz/bull-queue/services/main'
import { DateTime } from 'luxon'
import AppModel from './app_model.js'
import ZnPostCommentLike from './zn_post_comment_like.js'
import ZnUser from './zn_user.js'
import ZnPost from '#models/zn_post'
import ZnAdmin from './zn_admin.js'
import ZnChatMessage from './zn_chat_message.js'

export default class ZnPostComment extends AppModel {
  static table = 'zn_post_comments'

  @column({ columnName: 'resourceType' })
  declare resourceType: string

  @column({ columnName: 'resourceId' })
  declare resourceId: string

  @column({ columnName: 'postId' })
  // deprecated later, instead use resourceId with resourceType: post
  // @no-swagger
  declare postId: string

  @column({ columnName: 'userId' })
  // @example(0bbc952b-413f-4c9f-9a84-689dcaa4a3eb)
  declare userId: string

  @column({ columnName: 'userType' })
  // @example(ZnUser)
  declare userType: string

  @column({ columnName: 'parentCommentId' })
  // @example(0bbc952b-413f-4c9f-9a84-689dcaa4a3eb)
  declare parentCommentId: string | null

  @column({ columnName: 'content' })
  // @required @example(Content)
  declare content: string

  @column({ columnName: 'totalLike' })
  // use totalReact instead
  // @example(1)
  declare totalLike: number

  @column.dateTime({ columnName: 'lastEditedAt' })
  declare lastEditedAt: DateTime | null

  @hasMany(() => ZnPostComment, {
    foreignKey: 'parentCommentId',
    onQuery(query) {
      query.whereNull('deletedAt')
    },
  })
  public declare children: HasMany<typeof ZnPostComment>

  @manyToMany(() => ZnUser, {
    pivotTable: ZnPostCommentLike.table,
    pivotForeignKey: 'postCommentId',
    pivotRelatedForeignKey: 'userId',
    onQuery(query) {
      query.whereNull('zn_users.deletedAt')
    },
  })
  // @no-swagger
  declare reactUsers: ManyToMany<typeof ZnUser>

  @belongsTo(() => ZnUser, {
    foreignKey: 'userId',
  })
  // @no-swagger
  declare user: BelongsTo<typeof ZnUser>

  @belongsTo(() => ZnAdmin, {
    foreignKey: 'userId',
  })
  // @no-swagger
  declare admin: BelongsTo<typeof ZnAdmin>

  @belongsTo(() => ZnPost, {
    foreignKey: 'postId',
  })
  // @no-swagger
  declare post: BelongsTo<typeof ZnPost>

  @column({ columnName: 'chatMessageId' })
  declare chatMessageId: string | null

  @belongsTo(() => ZnChatMessage, {
    foreignKey: 'chatMessageId',
  })
  declare chatMessage: BelongsTo<typeof ZnChatMessage>

  @afterCreate()
  static async notificationNewComment(comment: ZnPostComment) {
    // no notification for new comment during live stream
    if (comment.resourceType == 'post') {
      const post = await ZnPost.query()
        .preload('stream')
        .where('id', comment.resourceId)
        .first()

      if (post?.stream?.isLive) { return }
    }

    await queue.dispatch(SendNotificationPostCommentJob, {
      comment,
      type: CommentJobType.Create,
      user: null,
      actor: {
        id: comment.userId,
        type: 'user',
      },
    })
  }

  serializeExtras() {
    return {
      totalChildren: this.$extras.children_count,
      totalReact: this.$extras.reactUsers_count,
    }
  }
}

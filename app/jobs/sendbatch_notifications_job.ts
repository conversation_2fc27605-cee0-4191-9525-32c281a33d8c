import { Job } from '@rlanz/bull-queue'
import {NotificationService} from "#services/notification_service";
import Zn<PERSON>ser from "#models/zn_user";

interface SendbatchNotificationsJobPayload {
  users: ZnUser[],
  notification: any,
  queueName: any
}

export default class SendbatchNotifications<PERSON>ob extends Job {
  private notificationService = new NotificationService()
  // This is the path to the file that is used to create the job
  static get $$filepath() {
    return import.meta.url
  }

  /**
   * Base Entry point
   */
  async handle(payload: SendbatchNotificationsJobPayload) {
    const {users, notification, queueName} = payload
    await this.notificationService._sendNotifications(users, notification, queueName)
  }

  /**
   * This is an optional method that gets called when the retries has exceeded and is marked failed.
   */
  async rescue(_: SendbatchNotificationsJobPayload) {}
}

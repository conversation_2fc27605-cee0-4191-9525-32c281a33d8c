// import ZurnoPostNotification from '#mails/zurno-post/zurno_post_notification'
import { ACTION, RESOURCE } from '#constants/authorization'
import { NOTIFICATION_TYPE } from '#constants/notification'
import ZurnoPostNotification from '#mails/zurno-post/zurno_post_notification'
import ZnAdmin from '#models/zn_admin'
import ZnPostComment from '#models/zn_post_comment'
import ZnUser from '#models/zn_user'
import { NotificationService } from '#services/notification_service'
import { Job } from '@rlanz/bull-queue'
import { AdminNotificationService } from '../../admin/services/notification/admin_notification_service.js'

export enum CommentJobType {
  Create = 'create',
  Like = 'Like',
}

interface SendNotificationPostCommentJobPayload {
  comment: ZnPostComment
  type: CommentJobType
  user: ZnUser | ZnAdmin | null
  actor?: { id: string; type: string }
}

export default class SendNotificationPostCommentJob extends Job {
  // This is the path to the file that is used to create the job
  static get $$filepath() {
    return import.meta.url
  }

  private adminNotificationService = new AdminNotificationService()

  /**
   * Base Entry point
   */
  async handle({ comment, type, user, actor }: SendNotificationPostCommentJobPayload) {
    switch (type) {
      case CommentJobType.Create: {
        if (comment.parentCommentId) {
          await this.replyComment(comment, actor)
        } else {
          await this.newComment(comment, actor)
        }
        break
      }
      case CommentJobType.Like: {
        await this.likeComment(comment, user, actor)
        break
      }
    }
  }

  /**
   * This is an optional method that gets called when the retries has exceeded and is marked failed.
   */
  async rescue() { }

  async likeComment(
    cmt: ZnPostComment,
    user: ZnUser | ZnAdmin | null,
    actor?: { id: string; type: string }
  ) {
    const comment = await ZnPostComment.query()
      .where({ id: cmt.id })
      .preload('user')
      .preload('admin')
      .first()

    if (!comment) {
      return
    }

    // don't send notification if user like own comment
    if (comment.userId == user?.id) {
      return
    }

    // send notification to comment's user
    if (comment.user) {
      this.send({
        title: `${user instanceof ZnUser ? user.firstName : user?.name || 'A user'} like your comment`,
        description: comment.content,
        type: NOTIFICATION_TYPE.POST_COMMENT,
        user: comment.user,
        comment,
        actorId: actor?.id ?? user?.id,
        actorType: 'user',
      })
    }

    // send notification to comment's admin
    if (comment.admin) {
      this.sendAdmin({
        title: `${user instanceof ZnUser ? user.firstName : user?.name || 'A user'} like ${comment.admin?.name || comment.admin?.username || 'an admin'}'s comment`,
        description: comment.content,
        type: NOTIFICATION_TYPE.POST_COMMENT,
        comment,
        actorId: actor?.id ?? user?.id,
        actorType: 'user',
      })
    }
  }

  async newComment(cmt: ZnPostComment, actor?: { id: string; type: string }) {
    const comment = await ZnPostComment.query()
      .where({ id: cmt.id })
      .preload('user')
      .preload('admin')
      .preload('post', (postQuery) => {
        postQuery.preload('user').preload('createdByAdmin')
      })
      .first()

    if (!comment || !comment.post) {
      return
    }

    // don't send notification if user comment on own post
    if (comment.userId == comment.post.userId) {
      return
    }

    // send notification to post's user
    if (comment.post.user) {
      this.send({
        title: `${comment.user?.firstName || 'A user'} add new comment to your post`,
        description: comment.content,
        type: NOTIFICATION_TYPE.POST_COMMENT,
        user: comment.post.user,
        comment,
        actorId: actor?.id ?? comment.user?.id,
        actorType: 'user',
      })
    }

    // send notification to post's admin
    if (comment.post.createdByAdmin) {
      this.sendAdmin({
        title: `${comment.admin
            ? comment.admin?.name || comment.admin?.username || 'An admin'
            : comment.user?.firstName || comment.user?.email || 'A user'
          } add new comment to  ${comment?.post?.createdByAdmin?.name || comment?.post?.createdByAdmin?.username || 'an admin'} 's post`,
        description: comment.content,
        type: NOTIFICATION_TYPE.POST_COMMENT,
        comment,
        actorId: actor?.id ?? comment.user?.id,
        actorType: 'user',
      })
    }
  }

  async replyComment(cmt: ZnPostComment, actor?: { id: string; type: string }) {
    const parentCommentId = cmt.parentCommentId
    if (!parentCommentId) {
      return
    }

    const comment = await ZnPostComment.query()
      .where({ id: cmt.id })
      .preload('user')
      .preload('admin')
      .preload('post', (postQuery) => {
        postQuery.preload('user')
      })
      .first()

    if (!comment) {
      return
    }

    const parentComment = await ZnPostComment.query()
      .where({ id: parentCommentId })
      .preload('user')
      .preload('admin')
      .preload('post')
      .first()

    if (!parentComment) {
      return
    }

    // don't send notification if user replies to self
    if (comment.userId == parentComment?.userId) {
      return
    }

    // send notification to parent comment's user
    if (parentComment.user) {
      this.send({
        title: `${comment.user?.firstName || 'A user'} replied to your comment`,
        description: comment.content,
        type: NOTIFICATION_TYPE.REPLY_COMMENT,
        user: parentComment.user,
        comment: parentComment,
        actorId: actor?.id ?? comment.user?.id,
        actorType: 'user',
      })
    }

    // send notification to parent comment's admin
    if (parentComment.admin) {
      this.sendAdmin({
        title: `${comment.admin
            ? comment.admin?.name || comment.admin?.username || 'An admin'
            : comment.user?.firstName || comment.user?.email || 'A user'
          } replied to  ${parentComment.admin?.name || parentComment.admin?.username || 'an admin'}'s comment`,
        description: comment.content,
        type: NOTIFICATION_TYPE.REPLY_COMMENT,
        comment: parentComment,
        actorId: actor?.id ?? comment.user?.id,
        actorType: 'user',
      })
    }
  }

  private async send({
    title,
    description,
    user,
    type,
    comment,
    actorId,
    actorType,
  }: {
    title: string
    description: string
    user: ZnUser
    type: number
    comment: ZnPostComment
    actorId?: string | null
    actorType?: string | null
  }) {
    try {
      const notificationService = new NotificationService()
      await notificationService.createIfNotExists({
        userId: user?.id,
        resourceId: comment.id,
        type,
        title,
        description,
        actorId,
        actorType,
        rootResourceId: comment.resourceId,
        rootResourceType: comment.resourceType,
      })
    } catch (error) {
      console.log(error)
    }
  }

  private async sendAdmin({
    title,
    description,
    type,
    comment,
    actorId,
    actorType,
  }: {
    title: string
    description: string
    type: number
    comment: ZnPostComment
    actorId?: string | null
    actorType?: string | null
  }) {
    try {
      // const admins = await ZnAdmin.all()
      const admins = await this.adminNotificationService.getAdminsByPermissions([
        { action: ACTION.READ, resource: RESOURCE.POST }
      ])
      for (const admin of admins) {
        await this.adminNotificationService.sendNotification({
          adminId: admin.serialize().id,
          resourceId: comment.id,
          type,
          title,
          description,
          actorId,
          actorType,
          rootResourceId: comment.resourceId,
          rootResourceType: comment.resourceType,
        })
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(admin.username)) {
          continue
        }
        await this.adminNotificationService.sendMail(
          new ZurnoPostNotification(
            admin.serialize().username,
            comment.user?.fullname || comment.user?.email || 'A user',
            title,
            comment.post?.title || 'Post',
            comment.content || ''
          )
        )
      }
    } catch (error) {
      console.log(error)
    }
  }
}

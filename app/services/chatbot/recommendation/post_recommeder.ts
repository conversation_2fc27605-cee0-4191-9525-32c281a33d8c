import {<PERSON>Recommender} from "#services/chatbot/recommendation/base_recommender";
import PostEmbeddingService from "#services/pinecone/post_embedding_service";

interface LocationConstraint {
  latitude: number
  longitude: number
  radius: number
}

export default class PostRecommender extends BaseRecommender {

  private buildFilter(location?: LocationConstraint): Record<string, any> | undefined {
    if (!location) return undefined

    const { latitude, longitude, radius } = location
    const latRadius = radius / 111
    const lonRadius = radius / (111 * Math.cos(latitude * Math.PI / 180))

    const minLat = latitude - latRadius
    const maxLat = latitude + latRadius
    const minLon = longitude - lonRadius
    const maxLon = longitude + lonRadius

    return {
      lat: { $gte: minLat, $lte: maxLat },
      lon: { $gte: minLon, $lte: maxLon },
    }
  }

  protected embeddingService = new PostEmbeddingService()

  async query(
    sentence: string,
    k = 5,
    constraints: { location?: LocationConstraint } = {}
  ) {
    const filter = this.buildFilter(constraints.location)
    return super.query(sentence, k, filter)
  }
}

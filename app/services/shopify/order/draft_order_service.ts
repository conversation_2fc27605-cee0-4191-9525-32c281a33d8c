import { BUNDLE_DISCOUNT_TYPE } from '#constants/bundle_product'
import ZnBundleProductDiscount from '#models/zn_bundle_product_discount'
import ZnBundleProductItem from '#models/zn_bundle_product_item'
import ZnGift from '#models/zn_gift'
import ZnProductVariant from '#models/zn_product_variant'
import CheckoutService from '#services/checkout_service'
import { GiftService } from '#services/shop/gift_service'
import db from '@adonisjs/lucid/services/db'
import { getShopifyCustomerId } from '../../../../services/commons.js'
import { formatOrderCart } from '../../../../services/shopify/cart/cart_format.js'
import { ShopifyService } from '../shopify_service.js'
import {
  EDiscountValueType,
  IDraftOrderServiceCreate,
  IDraftOrderUpdateAddress,
  IDraftOrderUpdateCustomAttributes,
  IDraftOrderUpdateDiscountCodes,
  IDraftOrderUpdateLineItems,
  IDraftOrderUpdateNote,
  IDraftOrderUpdateTags,
  ILineItem,
} from './draft_order.js'

export class DraftOrderService {
  private shopifyService: ShopifyService

  constructor() {
    this.shopifyService = new ShopifyService()
  }

  async create({
    user,
    shippingAddress,
    billingAddress,
    lineItems,
    discountCodes,
    note,
    tags,
    customAttributes,
  }: IDraftOrderServiceCreate) {
    let input = {
      customerId: user.shopifyCustomerId ? getShopifyCustomerId(user.shopifyCustomerId) : null,
      email: user.email,
      lineItems,
      billingAddress,
      shippingAddress,
      discountCodes,
      note,
      tags,
      customAttributes: customAttributes || [],
    } as any
    const calculatorDraftOrder = await this.shopifyService.calculatorDraftOrder(input)
    const shippingRateHandle = calculatorDraftOrder?.availableShippingRates?.[0]?.handle
    if (shippingRateHandle) {
      input.shippingLine = {
        shippingRateHandle,
      }
    }
    const variantIds = (
      await ZnProductVariant.query()
        .whereIn(
          'shopifyVariantId',
          lineItems.map((item) => item.variantId)
        )
        .select('id')
    ).map((variant) => variant.id)

    // Add gift toOrder
    const amount = Number(calculatorDraftOrder?.subtotalPriceSet?.shopMoney?.amount || 0)
    const giftService = new GiftService()
    const gift = await giftService.getGiftVariantsForCheckout({
      userId: user.id,
      amount,
      variantIds,
    })

    if (gift) {
      input = await this.applyGift({
        input,
        gift
      })
    }

    // Merge items
    const finalLineItems = [] as ILineItem[]
    for (const item of input.lineItems) {
      const compareKey = this.getCompareKey(item)
      const existItem = finalLineItems.find((i) => this.getCompareKey(i) === compareKey)
      if (existItem) {
        existItem.quantity += item.quantity
      } else {
        finalLineItems.push({
          ...item,
        })
      }
    }
    input.lineItems = finalLineItems

    const order = await this.shopifyService.createDraftOrder(input)
    if (order) {
      order.customAttributes = input?.customAttributes || []
    }

    return order
  }

  private getCompareKey(item: ILineItem) {
    return [
      item.variantId,
      item?.appliedDiscount?.title,
      item?.appliedDiscount?.value,
      item?.appliedDiscount?.valueType,
    ]
      .filter(Boolean)
      .join('-')
  }

  async applyGift({
    input,
    gift,
    excludeVariantIds = [],
  }: {
    input: any
    gift: ZnGift
    excludeVariantIds?: string[]
  }) {
    const variants = gift.variants?.filter(
      (variant) => variant.availableForSale && !excludeVariantIds.includes(variant.id)
    )
    if (variants.length > 0) {
      const checkoutService = new CheckoutService()
      for (const variant of variants) {
        const appliedDiscount = checkoutService.generateDiscountForGift({
          giftPrice: variant.$extras.pivot_giftPrice,
          price: variant.price,
        })
        let data = {
          variantId: variant.shopifyVariantId,
          quantity: 1,
          appliedDiscount,
          customAttributes: [
            {
              key: 'giftId',
              value: gift.id,
            },
          ],
        } as any
        input.lineItems.push(data)
      }
      input.customAttributes.push({
        key: 'giftId',
        value: gift.id,
      })
    }

    return input
  }

  async applyDiscount(
    shopifyVariantId: string,
    fastBundleDiscountId?: string,
    discountPrice?: number
  ) {
    const variant = await ZnProductVariant.findBy('shopifyVariantId', shopifyVariantId)
    // Check if the variant exists
    if (!variant) {
      return undefined
    }

    // Check if the fast bundle discount id exists
    if (!fastBundleDiscountId) {
      return undefined
    }

    const fastBundleDiscount = await ZnBundleProductDiscount.query()
      .where('fastBundleId', fastBundleDiscountId)
      .preload('bundleProduct')
      .first()
    // Check if the bundle product discount exists
    if (!fastBundleDiscount) {
      return undefined
    }
    // Check if the bundle product is active
    if (!fastBundleDiscount?.bundleProduct?.isActive) {
      return undefined
    }

    // Check if the variant is in the bundle product item
    const bundleProduct = await db
      .from(ZnBundleProductItem.table + ' as bpi')
      .join('zn_bundle_product_item_variants as bpi_v', 'bpi_v.bundleProductItemId', 'bpi.id')
      .where('bpi.bundleProductId', fastBundleDiscount.bundleProductId)
      .where('bpi_v.variantId', variant.id)
      .first()

    // Check if the bundle product item exists
    if (!bundleProduct) {
      return undefined
    }

    // apply the discount
    const title = 'BUNDLE'
    return {
      title,
      value:
        fastBundleDiscount.type === BUNDLE_DISCOUNT_TYPE.PER
          ? parseFloat(fastBundleDiscount.value.toString())
          : discountPrice,
      valueType:
        fastBundleDiscount.type === BUNDLE_DISCOUNT_TYPE.PER
          ? EDiscountValueType.PERCENTAGE
          : EDiscountValueType.FIXED_AMOUNT,
      description: title,
    }
  }

  async getDraftOrderById(id: string) {
    const draftOrder = await this.shopifyService.getDraftOrder(id)

    return draftOrder ? formatOrderCart(draftOrder) : null
  }

  async updateDraftOrderLineItems(input: IDraftOrderUpdateLineItems) {
    const draftOrder = await this.shopifyService.updateDraftOrder(input)

    return formatOrderCart(draftOrder)
  }

  async updateDraftOrderNote(data: IDraftOrderUpdateNote) {
    const draftOrder = await this.shopifyService.updateDraftOrder(data)

    return formatOrderCart(draftOrder)
  }

  async updateDraftOrderTags(data: IDraftOrderUpdateTags) {
    const draftOrder = await this.shopifyService.updateDraftOrder(data)

    return formatOrderCart(draftOrder)
  }

  async updateDraftOrderCustomAttributes(data: IDraftOrderUpdateCustomAttributes) {
    const draftOrder = await this.shopifyService.updateDraftOrder(data)

    return formatOrderCart(draftOrder)
  }

  async updateDraftOrderDiscountCodes(data: IDraftOrderUpdateDiscountCodes) {
    const draftOrder = await this.shopifyService.updateDraftOrder(data)

    return formatOrderCart(draftOrder)
  }

  async updateDraftOrderAddress(data: IDraftOrderUpdateAddress) {
    const draftOrder = await this.shopifyService.updateDraftOrder(data)

    return formatOrderCart(draftOrder)
  }

  async deleteDraftOrder(id: string) {
    const draftOrder = await this.shopifyService.deleteDraftOrder(id)

    return formatOrderCart(draftOrder)
  }
}

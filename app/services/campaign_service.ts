import <PERSON><PERSON>ampaignJob from '#jobs/send_campaign_job'
import Send<PERSON>ampaignMailJob from '#jobs/send_campaign_mail_job'
import Send<PERSON>ampaignSMSJob from '#jobs/send_campaign_sms_job'
import ZnCampaign, { ECampaignStatus } from '#models/zn_campaign'
import ZnCity from '#models/zn_city'
import ZnCountry from '#models/zn_country'
import ZnState from '#models/zn_state'
import ZnUser from '#models/zn_user'
import env from '#start/env'
import drive from '@adonisjs/drive/services/main'
import db from '@adonisjs/lucid/services/db'
import queue from '@rlanz/bull-queue/services/main'
import edge from 'edge.js'

export class CampaignService {
  constructor() { }

  async getCampaignUsers(
    input: {
      campaignId?: string
      includedIds: string[]
      excludedIds: string[]

      countryIds?: string[]
      stateIds?: string[]
      cityIds?: string[]

      tags: string[]
    },
    paginate?: {
      page: number
      limit?: number
    }
  ) {
    // get user ids based on product tags
    const tags = input.tags.filter((tag) => tag !== '') || []

    const usersQueryBasedOnTags = db
      .from('zn_users')
      .join('zn_orders', 'zn_orders.userId', 'zn_users.id')
      .join('zn_order_details', 'zn_order_details.orderId', 'zn_orders.id')
      .join('zn_product_variants', 'zn_product_variants.id', 'zn_order_details.variantId')
      .join('zn_products', 'zn_products.id', 'zn_product_variants.productId')
      .join('zn_products_product_tags', 'zn_products_product_tags.productId', 'zn_products.id')
      .join('zn_product_tags', 'zn_product_tags.id', 'zn_products_product_tags.productTagId')
      .whereIn('zn_product_tags.name', tags)
      .select(db.raw('DISTINCT zn_users.id'))

    usersQueryBasedOnTags.whereNull('zn_users.deletedAt')
    usersQueryBasedOnTags.whereNull('zn_orders.deletedAt')
    usersQueryBasedOnTags.whereNull('zn_order_details.deletedAt')
    usersQueryBasedOnTags.whereNull('zn_product_variants.deletedAt')
    usersQueryBasedOnTags.whereNull('zn_products.deletedAt')
    usersQueryBasedOnTags.whereNull('zn_product_tags.deletedAt')

    const usersBasedOnTags = await usersQueryBasedOnTags

    // get user ids based on campaignId
    let campaignUserIds: string[] = []
    if (input.campaignId) {
      const campaign = await ZnCampaign.query()
        .preload('users')
        .where('id', input.campaignId)
        .first()

      campaignUserIds = campaign?.users.map((user) => user.id) || []
    }

    const userIds = [
      ...usersBasedOnTags.map((user) => user.id),
      ...campaignUserIds,
      ...input.includedIds,
    ].filter((id) => !input.excludedIds.includes(id))

    const userQuery = ZnUser.query().whereIn('id', userIds)

    // get user ids based on locations
    if (input.countryIds?.length || input.stateIds?.length || input.cityIds?.length) {
      let countries: ZnCountry[]
      let states: ZnState[]
      let cities: ZnCity[]

      if (input.countryIds?.length) {
        countries = await ZnCountry.findMany(input.countryIds)
      }

      if (input.stateIds?.length) {
        states = await ZnState.findMany(input.stateIds)
      }

      if (input.cityIds?.length) {
        cities = await ZnCity.findMany(input.cityIds)
      }

      userQuery.orWhereHas('addresses', (addressesQuery) => {
        if (countries?.length) {
          addressesQuery
            .whereIn('country', countries.map(country => country.name))
        }

        if (states?.length) {
          addressesQuery
            .whereIn('province', states.map(state => state.name))
        }

        if (cities?.length) {
          addressesQuery
            .whereIn('city', cities.map(city => city.name))
        }
      })
    }

    let result
    if (paginate) {
      result = await userQuery.paginate(paginate.page, paginate.limit || 10)
    } else {
      result = await userQuery
    }

    return result
  }

  async sendNotification(campaignId: string) {
    // Fetch the campaign with user count
    const campaign = await ZnCampaign.query()
      .where('id', campaignId)
      .withCount('users')
      .first();

    if (!campaign) {
      return;
    }

    const limit = 500; // Adjust the limit based on system capacity
    let paginatedUserIdsQuery;

    // Determine the query source
    if (campaign.serializeExtras().usersCount > 0) {
      paginatedUserIdsQuery = db
        .query()
        .from('zn_campaigns_users')
        .where('campaignId', campaign.id)
        .select('userId AS id');
    } else {
      paginatedUserIdsQuery = ZnUser.query()
        .whereNotNull('lastLoginAt')
        .select('id');
    }

    // Get the total number of users
    const totalCount = (await paginatedUserIdsQuery.clone()).length
    const totalUsers = totalCount || 0;
    const lastPage = Math.ceil(totalUsers / limit);

    console.log(`Total Users: ${totalUsers}, Pages: ${lastPage}`);

    for (let page = 1; page <= lastPage; page++) {
      // Efficient pagination without loading entire data
      const usersBatch = await paginatedUserIdsQuery
        .clone()
        .paginate(page, limit);

      const userIds = usersBatch.toJSON().data.map((user) => user.id);
      console.log(`Processing page ${page} with ${userIds.length} users...`);

      if (userIds.length > 0) {
        // Send Campaign Notifications
        await Promise.all([
          this.sendCampaign({ campaign, userIds }),
          campaign.sms ? this.sendSMS({ campaign, userIds, message: campaign.sms }) : null,
          campaign.mailName ? this.sendMail({ campaign, userIds, mailName: campaign.mailName }) : null,
        ]);
      }
    }

    // Update the campaign status
    campaign.status = ECampaignStatus.Sent;
    await campaign.save();
  }

  async sendCampaign(input: {
    campaign: ZnCampaign
    // delay?: number,
    userIds: string[]
  }) {
    await queue.dispatch(SendCampaignJob, {
      campaign: input.campaign,
      userIds: input.userIds,
    })
  }

  async sendSMS(input: {
    campaign: ZnCampaign
    message: string
    // delay?: number,
    userIds: string[]
  }) {
    await queue.dispatch(SendCampaignSMSJob, {
      campaign: input.campaign,
      message: input.message,
      userIds: input.userIds,
    })
  }

  async sendMail(input: {
    campaign: ZnCampaign
    mailName: string
    // delay?: number,
    userIds: string[]
  }) {
    await queue.dispatch(SendCampaignMailJob, {
      campaign: input.campaign,
      mailName: input.mailName,
      userIds: input.userIds,
    })
  }

  async listMailTemplates(input: { page: number; search: string }) {
    const baseUrl = env.get('BASE_URL')

    const disk = drive.use('campaign')
    const response = await disk.listAll()

    const templates = []

    for (let item of response.objects) {
      if (item.isFile) {
        const name = item.name.split('.')[0]

        const html = await edge.render('mails/campaign/' + name, { serverDomain: baseUrl })

        templates.push({ name, html })
      }
    }

    const limit = 5
    const firstIdx = (input.page - 1) * limit
    const lastIdx = firstIdx + limit

    const paginated = {
      meta: {
        lastPage: Math.ceil(templates.length / limit),
        total: templates.length,
      },
      data: templates.slice(firstIdx, lastIdx),
    }

    return paginated
  }

  async getMailTemplate(mailName: string) {
    const baseUrl = env.get('BASE_URL')

    const html = await edge.render('mails/campaign/' + mailName, { serverDomain: baseUrl })

    return html
  }
}

import RemindVerifyStoreNotificationJob from '#jobs/remind_verify_store_job'
import SendNotificationJob from '#jobs/send_notification_job'
import ApproveClaimStoreNotification from '#mails/claim-store/approve_claim_store_notification'
import ClaimStoreNotification from '#mails/claim-store/claim_store_notification'
import NotifyClaimStoreNotification from '#mails/claim-store/receive_claim_store_notification'
import Notification from '#models/notification'
import ZnStore from '#models/zn_store'
import ZnUser from '#models/zn_user'
import env from '#start/env'
import mail from '@adonisjs/mail/services/main'
import queue from '@rlanz/bull-queue/services/main'
import { DateTime } from 'luxon'
import { NOTIFICATION_TYPE } from '../constants/notification.js'

export class StoreService {
  private serverDomain = env.get('BASE_URL') || ''
  private emailSupport = env.get('SUPPORT_EMAIL')

  constructor() {}
  async sendApprovalNotification(user: ZnUser, store: ZnStore) {
    if (user)
      await mail.sendLater(
        new ApproveClaimStoreNotification(
          user?.email,
          `${user.firstName} ${user.lastName}`,
          store?.name,
          store?.latitude,
          store?.longitude,
          store?.address,
          store?.phoneNumber,
          store?.website,
          store?.country?.name,
          store?.state?.name,
          store?.city?.name,
          this.serverDomain
        )
      )

    const notificationDataOfCreatedCampaign = {
      title: 'Approve Claim Store Request',
      description: `Your request to claim ownership of store ${store?.name} has been approved!`,
      type: NOTIFICATION_TYPE.CLAIM_STORE,
      resourceId: store?.id,
      actorId: user?.id,
      actorType: 'user',
    }

    const notification = await Notification.create({
      ...notificationDataOfCreatedCampaign,
      userId: user?.id,
    })

    // this.notificationService.send(user as any, notification)
    if (user)
      await queue.dispatch(SendNotificationJob, { users: [user], notifications: [notification] })
  }

  async sendClaimRequestEmail(user: ZnUser, store: ZnStore) {
    await mail.sendLater(
      new ClaimStoreNotification(
        this.emailSupport,
        user.email,
        `${user.firstName} ${user.lastName}`,
        user.phone,
        store?.name,
        store?.latitude,
        store?.longitude,
        store?.address,
        store?.phoneNumber,
        store?.website,
        store?.country?.name,
        store?.state?.name,
        store?.city?.name,
        this.serverDomain
      )
    )

    await mail.sendLater(
      new NotifyClaimStoreNotification(
        user.email,
        user.email,
        `${user.firstName} ${user.lastName}`,
        store?.name,
        store?.latitude,
        store?.longitude,
        store?.address,
        store?.phoneNumber,
        store?.website,
        store?.country?.name,
        store?.state?.name,
        store?.city?.name,
        this.serverDomain
      )
    )
  }

  async remindVerifyStore() {
    try {
      const pageSize = 50
      let currentPage = 1
      let hasNextPage = true

      const now = DateTime.local()
      const oneMonthAgo = now.minus({ months: 1 })

      while (hasNextPage) {
        const stores = await ZnStore.query()
          .whereHas('user', (userQuery) => {
            userQuery.whereNotNull('lastLoginAt')
          })
          .where('verified', 0)
          // .where('createdAt', '<=', oneMonthAgo)
          .whereRaw('DAY(createdAt) = ?', [oneMonthAgo.day]) // Compare the day of the month
          .whereRaw('MONTH(createdAt) <= ?', [oneMonthAgo.month]) // Ensure it was at least a month ago
          .preload('user')
          .paginate(currentPage, pageSize)

        hasNextPage = stores.hasMorePages

        for (const store of stores) {
          const user = store.user.serialize()

          if (user) {
            await queue.dispatch(RemindVerifyStoreNotificationJob, {
              userId: user.id,
              storeId: store.id,
            })
          }
        }

        currentPage++
      }

      console.log('Complete remind verify store!')
    } catch (error) {
      console.log(error)
    }
  }

  async getZurnoStore() {
    return await ZnStore.query().where('isZurno', true).first()
  }
}

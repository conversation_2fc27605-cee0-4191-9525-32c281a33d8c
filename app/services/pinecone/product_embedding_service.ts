import {PineconeEmbeddingService} from "#services/pinecone/pinecone_embedding_service";
import ZnProduct from "#models/zn_product";
import env from "#start/env";
import {htmlToText} from "../../../services/commons.js";
import {RecordMetadata} from "@pinecone-database/pinecone";

export default class ProductEmbeddingService extends PineconeEmbeddingService<ZnProduct> {
  private readonly rerankModel = 'bge-reranker-v2-m3'

  constructor() {
    const indexName = env.get('PROD_EMBEDDING_INDEX_NAME') ?? 'DUMPSTER'
    const embeddingModel = 'text-embedding-3-large'
    const dimension = 3072

    super(indexName, embeddingModel, dimension)
  }

  protected getId(product: ZnProduct) {
    return product.id
  }

  protected buildText(product: ZnProduct): string {
    const title       = product.title ?? ''
    const vendor      = product.vendor?.companyName ?? ''
    const type        = product.productType?.name ?? ''
    const collections = product.collections?.map(collection => collection.title).filter(Boolean).join(', ') ?? ''
    const body        = htmlToText(product.description ?? '').trim()
    const tags = product.tags.map( tag => tag.name) ?? []

    return [
      `Title: ${title} by ${vendor}.`,
      type        && `Category: ${type}.`,
      collections && `Collections: ${collections}.`,
      `Description: ${body}.`,
      `Tags: ${tags.join(', ')}`,
    ]
      .filter(Boolean)
      .join(' ')
  }

  protected rerankText(product: ZnProduct): string {
    const rating   = Number(product.reviewSummary?.averageRating ?? 0)
    const stockQty = Math.max(product.variant?.inventoryQuantity ?? 0, 0)

    return [
      rating   ? `Rating ${rating.toFixed(1)} / 5` : 'Unrated',
      stockQty ? `In stock (${stockQty})`          : 'Out of stock',
    ].join('. ')
  }

  /* ───────────────────────────── metadata saved with each vector ─────────────────────────── */
  protected buildMetadata(product: ZnProduct): RecordMetadata {
    const stockQty     = Math.max(product.variant?.inventoryQuantity ?? 0, 0)
    const reviewRating = Number(product.reviewSummary?.averageRating ?? 0)
    const reviewCount  = Number(product.reviewSummary?.totalReviews  ?? 0)
    const tags = product.tags.map( tag => tag.name) ?? []

    return {
      title        : product.title,
      vendor       : product.vendor?.companyName ?? '',
      productType  : product.productType?.name ?? '',
      collectionIds: product.collections?.map(collection => collection.id) ?? [],
      tags        : tags,
      price  : Number(product.price) || 0,
      description : this.buildText(product),
      rerankText  : this.rerankText(product),
      inStock     : stockQty > 0,
      stockQty,
      reviewRating,
      reviewCount,
    }
  }

  async query(text: string, topK = 10, filter?: Record<string, unknown>) {
    const queryResults = await super.query(text, topK * 3, filter)
    const docs   = queryResults.map(result => ({ id: result.id, text: result.metadata?.rerankText as string }))

    const rerank = await this.pinecone.inference.rerank(
      this.rerankModel,
      text,
      docs,
      { topN: topK, rankFields: ['text'], returnDocuments: false },
    )

    return rerank.data.map(r => queryResults[r.index])
  }


}

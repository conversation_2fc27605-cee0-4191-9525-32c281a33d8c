import Notification from '#models/notification'
import ZnUser from '#models/zn_user'
import queue from '@rlanz/bull-queue/services/main'
import * as firebase from 'firebase-admin'
import { FirebaseService } from '../../services/firebase/index.js'
import { NOTIFICATION_TYPE } from '#constants/notification'
import SendNotificationJob from '#jobs/send_notification_job'
import SendbatchNotificationsJob from '#jobs/sendbatch_notifications_job'

export class NotificationService {
  private messagingService: firebase.messaging.Messaging
  private databaseRealtime: firebase.database.Database
  private apns = {
    payload: {
      aps: {
        'content-available': 1,
      },
    },
  }

  constructor() {
    const firebaseInstance = new FirebaseService()
    this.messagingService = firebaseInstance?.getMessaging()
    this.databaseRealtime = firebaseInstance.getDatabase()
  }

  async send(users: ZnUser[], notifications: Notification[]) {
    // Filter out users who have disabled post notifications
    const usersToNotify = users
      .filter((user) => user.receivePostNotifications && user.deviceToken)
      .map((user: any) => user?.deviceToken)

    if (usersToNotify.length > 0) {
      await this.messagingService.sendEachForMulticast({
        tokens: usersToNotify as string[],
        data: {
          id: notifications[0].id,
          title: notifications[0].title,
          body: notifications[0].description,
          resourceId: notifications[0]?.resourceId ?? '',
          type: notifications[0].type.toString(),
        },
        apns: this.apns,
      })
    }

    await Promise.all(
      notifications.map((notification) =>
        this.databaseRealtime
          .ref('users/' + notification?.userId + '/notifications/' + notification.id)
          .set({
            id: notification.id,
            title: notification.title,
            description: notification.description,
            resourceId: notification.resourceId ?? '',
            isRead: notification.isRead ?? 0,
          })
      )
    )
  }

  async sendCampaign(data: any) {
    const message = {
      data: {
        title: data.title,
        body: data.description ?? '',
        id: data.id ?? '',
        resourceId: data?.resourceId ?? '',
        url: data?.url ?? '',
        campaignType: (data.type ?? '').toString(),
        type: data.type.toString(),
      },
      apns: this.apns,
      topic: 'campaign',
    }
    console.log('message', message)
    await this.messagingService.send(message)
  }

  async sendToUsers(data: any, userIds: string[]) {
    const users = await ZnUser.findMany(userIds)
    await this._sendNotifications(users, data)
  }

  async sendToAll(data: any) {
    const batchSize = 500 // Adjust based on server capacity
    let offset = 0
    let hasMoreUsers = true

    while (hasMoreUsers) {
      // Fetch users in batches
      const users = await ZnUser.query()
        .whereNotNull('lastLoginAt')
        .offset(offset).limit(batchSize)

      if (users.length === 0) {
        hasMoreUsers = false
      } else {
        // await this._sendNotifications(users, data, 'liveStream')
        await queue.dispatch(
          SendbatchNotificationsJob,
          { users, notification: data, queueName: 'liveStream' },
          { queueName: 'liveStream' }
        )
        offset += batchSize // Move to the next batch
      }
    }
  }

  async _sendNotifications(users: ZnUser[], data: Notification, queueName = 'notification') {
    let tokens: Set<string> = new Set() // Using Set to prevent duplicates

    const notifications: any[] = []
    const userData: any[] = []

    for (const user of users) {
      if (user) {
        // Check for existing notification
        // @ts-ignore
        let existenceCondition: any = {
          type: data.type,
          resourceId: data.resourceId,
          userId: user.id,
        }

        //Check for campaign
        if (data.type == NOTIFICATION_TYPE.NOTIFICATION && data.campaignId) {
          existenceCondition = {
            type: data.type,
            resourceId: data.resourceId,
            campaignId: data.campaignId,
            userId: user.id,
          }
        }

        // @ts-ignore
        const existingNotification = await Notification.query().where(existenceCondition).first()

        if (!existingNotification) {
          const notification = { ...data, userId: user.id }
          notifications.push(notification)
          if (user.deviceToken && !tokens.has(user.deviceToken)) {
            tokens.add(user.deviceToken)
            userData.push(user)
          }
        }
      }
    }

    if (notifications && notifications.length > 0) {
      const notificationData = await Notification.createMany(notifications)
      await queue.dispatch(
        SendNotificationJob,
        { users: userData, notifications: notificationData },
        { queueName: queueName }
      )
    }
  }

  async update(notification: Notification) {
    await this.databaseRealtime
      .ref('users/' + notification.userId + '/notifications/' + notification.id)
      .remove()
  }

  async removeAllForUser(userId: string) {
    await this.databaseRealtime.ref('users/' + userId + '/notifications').remove()
  }

  async createIfNotExists({
    userId,
    resourceId,
    type,
    title,
    description,
    campaignId = null,
    resourceStatus = null,
    actorId = null,
    actorType = null,
    rootResourceId = null,
    rootResourceType = null,
  }: {
    userId?: string
    resourceId: string | null
    type: number
    title: string
    description: string
    campaignId?: string | null
    resourceStatus?: string | null
    actorId?: string | null
    actorType?: string | null
    rootResourceId?: string | null
    rootResourceType?: string | null
  }) {
    const existed = await Notification.query()
      .where({ userId: userId || '', resourceId, type, resourceStatus, actorId, actorType })
      .first()

    if (existed) return existed

    const notification = await Notification.create({
      userId,
      resourceId,
      type,
      title,
      description,
      campaignId,
      resourceStatus,
      actorId,
      actorType,
      rootResourceId: rootResourceId ?? undefined,
      rootResourceType: rootResourceType ?? undefined,
    })

    if (userId) {
      const user = await ZnUser.find(userId)
      if (user && notification) {
        await queue.dispatch(
          SendNotificationJob,
          { users: [user], notifications: [notification] },
          { queueName: 'notification' }
        )
      }
    }
    return notification
  }

  async removeNotificationsForUserByTypes(userId: string, types: number[]) {
    try {
      const notifications = await Notification.query()
        .where('userId', userId)
        .whereIn('type', types)
        .select('id')

      const ref = this.databaseRealtime.ref('users/' + userId + '/notifications')
      const updates: any = {}
      notifications.forEach((n) => {
        updates[n.id] = null
      })
      if (Object.keys(updates).length > 0) {
        ref.update(updates)
      }
    } catch (error) {
      console.error('Error removing notifications from Firebase:', error)
    }
  }

  /**
   * Mark a single notification as read and handle side effects
   */
  async markAsRead(notification: Notification) {
    if (!notification.isRead) {
      notification.isRead = true
      await notification.save()
      await this.update(notification)
    }
    return notification
  }

  /**
   * Mark all notifications for a user as read and handle side effects
   */
  async markAllAsReadForUser(userId: string) {
    await Notification.query().where({ userId }).update({ isRead: true })
    await this.removeAllForUser(userId)
  }

  /**
   * Mark all notifications for a user in a group as read and handle side effects
   */
  async markAllAsReadByGroup(userId: string, types: number[]) {
    await Notification.query()
      .where('userId', userId)
      .whereIn('type', types)
      .update({ isRead: true })
    await this.removeNotificationsForUserByTypes(userId, types)
  }
}

import env from '#start/env'
import axios from 'axios'

export class GeoService {
  private googleAPIUrl: string = 'https://maps.googleapis.com/maps/api/geocode/json?'
  private readonly googleApiKey: any

  constructor() {
    this.googleApiKey = env.get('GOOGLE_PLACES_API_KEY') // Set in .env
  }
  async getAddressFromLatLon(lat: number, lon: number) {
    let addressObject = {
      fullAddress: '',
      address1: '',
      city: '',
      county: '',
      state: '',
      province: '',
      country: '',
      zip: '',
      postalCode: '',
      postalCodeSuffix: '',
      provinceCode: '',
      countryCode: '',
      plusCode: '',
      placeId: '',
      latitude: lat,
      longitude: lon,
    }

    // 1. Try Google Maps API (if API key is set)
    if (this.googleApiKey) {
      try {
        const googleUrl = `${this.googleAPIUrl}latlng=${lat},${lon}&key=${this.googleApiKey}`
        const googleResponse = await axios.get(googleUrl)

        const results = googleResponse.data.results

        if (results.length > 0) {
          const firstResult = results[0]
          addressObject.fullAddress = firstResult.formatted_address
          addressObject.placeId = firstResult.place_id

          // Extract address components
          let streetNumber, route
          for (const component of firstResult.address_components) {
            if (component.types.includes('street_number')) {
              streetNumber = component.long_name // Street Number
            }
            if (component.types.includes('route')) {
              route = component.long_name // Route
            }
            if (component.types.includes('locality')) {
              addressObject.city = component.long_name // City
            }
            if (component.types.includes('administrative_area_level_2')) {
              addressObject.county = component.long_name // County
            }
            if (component.types.includes('administrative_area_level_1')) {
              addressObject.state = component.long_name // State
              addressObject.province = component.long_name
              addressObject.provinceCode = component.short_name
            }
            if (component.types.includes('country')) {
              addressObject.country = component.long_name // Country
              addressObject.countryCode = component.short_name
            }
            if (component.types.includes('postal_code')) {
              addressObject.postalCode = component.long_name // Postal Code
              addressObject.zip = component.long_name
            }
            if (component.types.includes('postal_code_suffix')) {
              addressObject.postalCodeSuffix = component.long_name // Postal Code Suffix
            }
          }

          if (streetNumber || route) {
            addressObject.address1 = (streetNumber + ' ' + route).trim()
          }

          // Extract Plus Code (if available)
          if (googleResponse.data.plus_code) {
            addressObject.plusCode = googleResponse.data.plus_code.compound_code
          }
        }
      } catch (error) {
        console.error('Google Geocoding API failed:', error.message)
      }
    }

    // 2. If Google API fails or is not set, fallback to OpenStreetMap
    if (!addressObject.fullAddress) {
      try {
        const osmUrl = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lon}`
        const osmResponse = await axios.get(osmUrl)

        if (osmResponse.data.address) {
          addressObject.fullAddress = osmResponse.data.display_name
          addressObject.state = osmResponse.data.address.state || null
          addressObject.city =
            osmResponse.data.address.city ||
            osmResponse.data.address.town ||
            osmResponse.data.address.village ||
            null
          addressObject.country = osmResponse.data.address.country || null
          addressObject.postalCode = osmResponse.data.address.postcode || null
        }
      } catch (error) {
        console.error('OpenStreetMap Geocoding API failed:', error.message)
      }
    }

    return addressObject
  }

  async getLatLonFromAddress(address: string) {
    const googleUrl = `${this.googleAPIUrl}address=${encodeURIComponent(address)}&key=${this.googleApiKey}`
    const googleResponse = await axios.get(googleUrl)

    const results = googleResponse.data.results
    if (results.length > 0) {
      const location = results[0].geometry.location
      return {
        latitude: location.lat,
        longitude: location.lng,
      }
    }

  }
}
